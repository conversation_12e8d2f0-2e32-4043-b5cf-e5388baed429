#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { Converter } from 'opencc-js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 初始化 OpenCC 转换器 - 简体到香港繁体
const converter = Converter({ from: 'cn', to: 'hk' })

// 递归转换对象中的所有中文文本
function convertObject(obj) {
  if (typeof obj === 'string') {
    // 只转换包含中文的字符串
    if (/[\u4e00-\u9fff]/.test(obj)) {
      return converter(obj)
    }
    return obj
  } else if (Array.isArray(obj)) {
    return obj.map(convertObject)
  } else if (obj !== null && typeof obj === 'object') {
    const converted = {}
    for (const [key, value] of Object.entries(obj)) {
      converted[key] = convertObject(value)
    }
    return converted
  }
  return obj
}

// 主函数
function main() {
  console.log('开始转换简体中文到香港繁体中文...')

  // 读取简体中文语言包
  const zhCNPath = path.join(__dirname, '..', 'src', 'locales', 'zh-CN.json')

  if (!fs.existsSync(zhCNPath)) {
    console.error('简体中文语言包不存在，请先运行 extract-chinese.js')
    process.exit(1)
  }

  try {
    const zhCNContent = fs.readFileSync(zhCNPath, 'utf-8')
    const zhCNData = JSON.parse(zhCNContent)

    console.log('正在转换文本...')

    // 转换为香港繁体
    const zhHKData = convertObject(zhCNData)

    // 保存香港繁体语言包
    const zhHKPath = path.join(__dirname, '..', 'src', 'locales', 'zh-HK.json')
    fs.writeFileSync(zhHKPath, JSON.stringify(zhHKData, null, 2), 'utf-8')

    console.log(`香港繁体语言包已保存到: ${zhHKPath}`)

    // 统计转换结果
    let totalTexts = 0
    let convertedTexts = 0

    function countTexts(obj, originalObj) {
      if (typeof obj === 'string' && typeof originalObj === 'string') {
        totalTexts++
        if (obj !== originalObj && /[\u4e00-\u9fff]/.test(originalObj)) {
          convertedTexts++
        }
      } else if (Array.isArray(obj)) {
        obj.forEach((item, index) => countTexts(item, originalObj[index]))
      } else if (obj !== null && typeof obj === 'object') {
        Object.keys(obj).forEach(key => {
          countTexts(obj[key], originalObj[key])
        })
      }
    }

    countTexts(zhHKData, zhCNData)

    console.log(`转换完成！`)
    console.log(`总文本数: ${totalTexts}`)
    console.log(`已转换: ${convertedTexts}`)
    console.log(`转换率: ${((convertedTexts / totalTexts) * 100).toFixed(1)}%`)

    // 显示一些转换示例
    console.log('\n转换示例:')
    const examples = [
      { category: 'auth', key: 'deng_lu', original: zhCNData.auth?.deng_lu, converted: zhHKData.auth?.deng_lu },
      { category: 'post', key: 'fa_bu', original: zhCNData.post?.fa_bu, converted: zhHKData.post?.fa_bu },
      { category: 'common', key: 'cheng_gong', original: zhCNData.common?.cheng_gong, converted: zhHKData.common?.cheng_gong }
    ]

    examples.forEach(example => {
      if (example.original && example.converted && example.original !== example.converted) {
        console.log(`${example.category}.${example.key}: "${example.original}" → "${example.converted}"`)
      }
    })

  } catch (error) {
    console.error('转换过程中出现错误:', error.message)
    process.exit(1)
  }
}

main()
