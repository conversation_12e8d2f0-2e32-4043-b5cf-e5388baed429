#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 中文字符正则表达式
const CHINESE_REGEX = /[\u4e00-\u9fff]+/g

// 需要扫描的文件扩展名
const SCAN_EXTENSIONS = ['.vue', '.ts', '.js']

// 忽略的目录
const IGNORE_DIRS = ['node_modules', 'dist', '.git', 'scripts']

// 存储提取的中文文本
const extractedTexts = new Set()
const textWithContext = []

// 递归扫描目录
function scanDirectory(dir) {
  const files = fs.readdirSync(dir)
  
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (!IGNORE_DIRS.includes(file)) {
        scanDirectory(filePath)
      }
    } else if (SCAN_EXTENSIONS.includes(path.extname(file))) {
      scanFile(filePath)
    }
  }
}

// 扫描单个文件
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf-8')
    const lines = content.split('\n')
    
    lines.forEach((line, index) => {
      const matches = line.match(CHINESE_REGEX)
      if (matches) {
        matches.forEach(match => {
          // 过滤掉单个字符和过短的文本
          if (match.length > 1) {
            extractedTexts.add(match)
            textWithContext.push({
              text: match,
              file: path.relative(path.join(__dirname, '..'), filePath),
              line: index + 1,
              context: line.trim()
            })
          }
        })
      }
    })
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message)
  }
}

// 生成语言包
function generateLanguagePack() {
  const languagePack = {}
  
  // 按类别组织文本
  const categories = {
    nav: ['首页', '帖子', '登录', '注册', '退出', '我的'],
    auth: ['登录', '注册', '密码', '邮箱', '用户名', '确认密码', '立即注册', '立即登录', '忘记密码'],
    post: ['发布', '帖子', '寻找中', '已找到', '已关闭', '走失', '宠物', '线索'],
    pet: ['狗', '猫', '兔子', '鸟类', '仓鼠', '雄性', '雌性', '黑色', '白色', '棕色', '金色', '灰色', '花色'],
    location: ['中西区', '湾仔区', '东区', '南区', '油尖旺区', '深水埗区', '九龙城区', '黄大仙区', '观塘区', '荃湾区', '屯门区', '元朗区', '北区', '大埔区', '沙田区', '西贡区', '葵青区', '离岛区'],
    message: ['成功', '失败', '错误', '警告', '提示', '确认', '取消', '保存', '删除', '编辑', '查看', '搜索'],
    common: ['确定', '取消', '保存', '删除', '编辑', '查看', '搜索', '加载中', '暂无数据', '操作成功', '操作失败']
  }
  
  // 为每个文本生成key
  const sortedTexts = Array.from(extractedTexts).sort()
  
  sortedTexts.forEach(text => {
    let category = 'common'
    let key = generateKey(text)
    
    // 尝试分类
    for (const [cat, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        category = cat
        break
      }
    }
    
    if (!languagePack[category]) {
      languagePack[category] = {}
    }
    
    // 确保key唯一
    let finalKey = key
    let counter = 1
    while (languagePack[category][finalKey]) {
      finalKey = `${key}_${counter}`
      counter++
    }
    
    languagePack[category][finalKey] = text
  })
  
  return languagePack
}

// 生成key
function generateKey(text) {
  // 简单的拼音映射（部分常用字）
  const pinyinMap = {
    '登': 'deng', '录': 'lu', '注': 'zhu', '册': 'ce', '密': 'mi', '码': 'ma',
    '邮': 'you', '箱': 'xiang', '用': 'yong', '户': 'hu', '名': 'ming',
    '首': 'shou', '页': 'ye', '帖': 'tie', '子': 'zi', '发': 'fa', '布': 'bu',
    '寻': 'xun', '找': 'zhao', '中': 'zhong', '已': 'yi', '到': 'dao',
    '关': 'guan', '闭': 'bi', '宠': 'chong', '物': 'wu', '线': 'xian', '索': 'suo',
    '成': 'cheng', '功': 'gong', '失': 'shi', '败': 'bai', '确': 'que', '定': 'ding',
    '取': 'qu', '消': 'xiao', '保': 'bao', '存': 'cun', '删': 'shan', '除': 'chu',
    '编': 'bian', '辑': 'ji', '查': 'cha', '看': 'kan', '搜': 'sou'
  }
  
  let key = ''
  for (const char of text) {
    if (pinyinMap[char]) {
      key += pinyinMap[char] + '_'
    }
  }
  
  // 如果无法转换，使用英文描述
  if (!key) {
    if (text.includes('登录')) key = 'login'
    else if (text.includes('注册')) key = 'register'
    else if (text.includes('密码')) key = 'password'
    else if (text.includes('邮箱')) key = 'email'
    else if (text.includes('用户')) key = 'user'
    else if (text.includes('首页')) key = 'home'
    else if (text.includes('帖子')) key = 'post'
    else if (text.includes('发布')) key = 'publish'
    else if (text.includes('寻找')) key = 'searching'
    else if (text.includes('找到')) key = 'found'
    else if (text.includes('关闭')) key = 'closed'
    else if (text.includes('宠物')) key = 'pet'
    else if (text.includes('成功')) key = 'success'
    else if (text.includes('失败')) key = 'failed'
    else key = 'text'
  }
  
  return key.replace(/_$/, '') || 'text'
}

// 主函数
function main() {
  console.log('开始扫描中文文本...')
  
  const srcDir = path.join(__dirname, '..', 'src')
  scanDirectory(srcDir)
  
  console.log(`共找到 ${extractedTexts.size} 个中文文本`)
  
  // 生成语言包
  const languagePack = generateLanguagePack()
  
  // 保存简体中文语言包
  const outputPath = path.join(__dirname, '..', 'src', 'locales', 'zh-CN.json')
  fs.writeFileSync(outputPath, JSON.stringify(languagePack, null, 2), 'utf-8')
  
  // 保存详细信息用于调试
  const debugPath = path.join(__dirname, '..', 'src', 'locales', 'extracted-texts.json')
  fs.writeFileSync(debugPath, JSON.stringify(textWithContext, null, 2), 'utf-8')
  
  console.log(`语言包已保存到: ${outputPath}`)
  console.log(`详细信息已保存到: ${debugPath}`)
  
  // 显示统计信息
  Object.entries(languagePack).forEach(([category, texts]) => {
    console.log(`${category}: ${Object.keys(texts).length} 个文本`)
  })
}

main()
