#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 手动整理的语言包结构
const organizedLanguagePack = {
  // 导航相关
  nav: {
    home: '首页',
    posts: '帖子',
    login: '登录',
    register: '注册',
    logout: '退出',
    dashboard: '个人中心',
    myPosts: '我的帖子',
    createPost: '发布信息'
  },

  // 认证相关
  auth: {
    login: '登录',
    loginTitle: '登录您的账户',
    register: '注册',
    registerTitle: '创建新账户',
    email: '邮箱地址',
    password: '密码',
    confirmPassword: '确认密码',
    username: '用户名',
    rememberMe: '记住我',
    forgotPassword: '忘记密码？',
    noAccount: '还没有账户？',
    hasAccount: '已有账户？',
    registerNow: '立即注册',
    loginNow: '立即登录',
    loginButton: '登录',
    registerButton: '注册',
    logout: '退出登录'
  },

  // 帖子相关
  post: {
    title: '帖子',
    create: '发布信息',
    createTitle: '发布走失宠物信息',
    edit: '编辑帖子',
    delete: '删除帖子',
    view: '查看详情',
    share: '分享',
    status: {
      searching: '寻找中',
      found: '已找到',
      closed: '已关闭'
    },
    actions: {
      publish: '发布',
      save: '保存',
      cancel: '取消',
      edit: '编辑',
      delete: '删除',
      close: '关闭帖子',
      reopen: '重新开启'
    },
    fields: {
      petName: '宠物名字',
      petSpecies: '宠物品种',
      petGender: '宠物性别',
      petColor: '主要毛色',
      petAge: '宠物年龄',
      description: '详细描述',
      location: '走失地点',
      contactInfo: '联系方式',
      reward: '酬谢金额',
      images: '宠物照片'
    }
  },

  // 宠物相关
  pet: {
    species: {
      dog: '狗',
      cat: '猫',
      rabbit: '兔子',
      bird: '鸟类',
      hamster: '仓鼠',
      other: '其他'
    },
    gender: {
      male: '雄性',
      female: '雌性',
      unknown: '不确定'
    },
    colors: {
      black: '黑色',
      white: '白色',
      brown: '棕色',
      golden: '金色',
      gray: '灰色',
      mixed: '花色',
      other: '其他'
    }
  },

  // 香港地区
  location: {
    districts: {
      central: '中西区',
      wanchai: '湾仔区',
      eastern: '东区',
      southern: '南区',
      yauTsimMong: '油尖旺区',
      shamShuiPo: '深水埗区',
      kowloonCity: '九龙城区',
      wongTaiSin: '黄大仙区',
      kwunTong: '观塘区',
      tsuen: '荃湾区',
      tuenMun: '屯门区',
      yuenLong: '元朗区',
      north: '北区',
      taiPo: '大埔区',
      shaTin: '沙田区',
      saiKung: '西贡区',
      kwaiTsing: '葵青区',
      islands: '离岛区'
    }
  },

  // 线索相关
  sighting: {
    title: '线索',
    add: '提供线索',
    view: '查看线索',
    description: '线索描述',
    location: '发现地点',
    time: '发现时间',
    contact: '联系方式',
    submit: '提交线索'
  },

  // 通用操作
  common: {
    save: '保存',
    cancel: '取消',
    delete: '删除',
    edit: '编辑',
    view: '查看',
    search: '搜索',
    filter: '筛选',
    sort: '排序',
    loading: '加载中...',
    noData: '暂无数据',
    confirm: '确认',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    submit: '提交',
    upload: '上传',
    download: '下载',
    share: '分享',
    copy: '复制',
    close: '关闭'
  },

  // 消息提示
  message: {
    success: {
      login: '登录成功',
      register: '注册成功',
      logout: '退出成功',
      save: '保存成功',
      delete: '删除成功',
      upload: '上传成功',
      submit: '提交成功'
    },
    error: {
      network: '网络连接失败，请检查网络设置',
      server: '服务器错误，请稍后重试',
      unauthorized: '登录已过期，请重新登录',
      forbidden: '没有权限执行此操作',
      notFound: '请求的资源不存在',
      validation: '输入信息有误，请检查后重试',
      fileTooLarge: '文件大小超过限制',
      invalidFileType: '不支持的文件类型'
    },
    confirm: {
      delete: '确定要删除吗？',
      logout: '确定要退出登录吗？',
      close: '确定要关闭帖子吗？'
    }
  },

  // 表单相关
  form: {
    required: '此字段为必填项',
    invalid: '格式不正确',
    tooShort: '内容过短',
    tooLong: '内容过长',
    emailInvalid: '邮箱格式不正确',
    passwordTooShort: '密码至少需要6位',
    passwordMismatch: '两次输入的密码不一致'
  },

  // 分页
  pagination: {
    previous: '上一页',
    next: '下一页',
    page: '第 {page} 页',
    total: '共 {total} 条',
    showing: '显示第 {start} 到 {end} 条，共 {total} 条'
  }
}

// 主函数
function main() {
  console.log('重新组织语言包结构...')
  
  // 保存新的简体中文语言包
  const zhCNPath = path.join(__dirname, '..', 'src', 'locales', 'zh-CN.json')
  fs.writeFileSync(zhCNPath, JSON.stringify(organizedLanguagePack, null, 2), 'utf-8')
  
  console.log(`新的简体中文语言包已保存到: ${zhCNPath}`)
  
  // 统计信息
  let totalKeys = 0
  function countKeys(obj) {
    Object.values(obj).forEach(value => {
      if (typeof value === 'string') {
        totalKeys++
      } else if (typeof value === 'object') {
        countKeys(value)
      }
    })
  }
  
  countKeys(organizedLanguagePack)
  console.log(`总计 ${totalKeys} 个翻译键`)
  
  // 显示各模块统计
  Object.entries(organizedLanguagePack).forEach(([module, content]) => {
    let moduleKeys = 0
    countKeys({ [module]: content })
    console.log(`${module}: ${moduleKeys} 个键`)
  })
}

main()
