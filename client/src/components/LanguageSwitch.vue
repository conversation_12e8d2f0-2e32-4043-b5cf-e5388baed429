<template>
  <div class="relative">
    <!-- 语言切换按钮 -->
    <button
      @click="toggleDropdown"
      class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
      :class="{ 'bg-gray-50': isOpen }"
    >
      <span class="text-lg">{{ currentLocaleInfo.flag }}</span>
      <span class="hidden sm:block">{{ currentLocaleInfo.name }}</span>
      <svg
        class="w-4 h-4 transition-transform"
        :class="{ 'rotate-180': isOpen }"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <!-- 下拉菜单 -->
    <div
      v-show="isOpen"
      class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
      @click.stop
    >
      <div class="py-1">
        <button
          v-for="locale in SUPPORTED_LOCALES"
          :key="locale.code"
          @click="switchLanguage(locale.code)"
          class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'bg-gray-50 text-primary-600': currentLocale === locale.code }"
        >
          <span class="text-lg mr-3">{{ locale.flag }}</span>
          <span>{{ locale.name }}</span>
          <svg
            v-if="currentLocale === locale.code"
            class="w-4 h-4 ml-auto text-primary-600"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- 点击外部关闭下拉菜单 -->
    <div
      v-if="isOpen"
      class="fixed inset-0 z-40"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { SUPPORTED_LOCALES, setLocale, getCurrentLocale, type SupportedLocale } from '@/locales'

const { locale } = useI18n()

const isOpen = ref(false)

// 当前语言
const currentLocale = computed(() => getCurrentLocale())

// 当前语言信息
const currentLocaleInfo = computed(() => {
  return SUPPORTED_LOCALES.find(l => l.code === currentLocale.value) || SUPPORTED_LOCALES[0]
})

// 切换语言
function switchLanguage(localeCode: SupportedLocale) {
  setLocale(localeCode)
  closeDropdown()
  
  // 可选：显示切换成功提示
  console.log(`语言已切换为: ${SUPPORTED_LOCALES.find(l => l.code === localeCode)?.name}`)
}

// 切换下拉菜单
function toggleDropdown() {
  isOpen.value = !isOpen.value
}

// 关闭下拉菜单
function closeDropdown() {
  isOpen.value = false
}

// 键盘事件处理
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape') {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* 确保下拉菜单在移动端也能正常显示 */
@media (max-width: 640px) {
  .absolute {
    right: 0;
    left: auto;
  }
}
</style>
