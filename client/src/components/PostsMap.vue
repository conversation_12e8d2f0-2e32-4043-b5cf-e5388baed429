<template>
  <div class="relative">
    <!-- 地图容器 -->
    <div
      ref="mapContainer"
      class="w-full h-96 rounded-lg border border-gray-300"
    ></div>

    <!-- 加载状态 -->
    <div
      v-if="loading || geocoding"
      class="absolute inset-0 bg-white bg-opacity-75 rounded-lg flex items-center justify-center"
    >
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
        <p class="mt-2 text-sm text-gray-600">
          {{ geocoding ? '正在定位地址...' : '加载地图中...' }}
        </p>
      </div>
    </div>

    <!-- 地图信息面板 -->
    <div class="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3 z-10">
      <div class="text-sm text-gray-600">
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 bg-red-500 rounded-full"></div>
          <span>走失位置</span>
        </div>
        <div class="mt-1 text-xs text-gray-500">
          找到 {{ validPosts.length }} 个走失信息
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div
      v-if="error && !loading"
      class="absolute inset-0 bg-white bg-opacity-90 rounded-lg flex items-center justify-center"
    >
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">地图加载失败</h3>
        <p class="mt-1 text-sm text-gray-500">{{ error }}</p>
        <button
          @click="initMap"
          class="mt-2 btn-secondary text-sm"
        >
          重新加载
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import L from 'leaflet'
import 'leaflet.markercluster'
import 'leaflet.markercluster/dist/MarkerCluster.css'
import 'leaflet.markercluster/dist/MarkerCluster.Default.css'
import type { Post } from '@/types'
import { MAP_CONFIG } from '@/constants'
import { getFullImageUrl } from '@/utils/helpers'

// 修复Leaflet图标问题
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
})

interface Props {
  posts: Post[]
  loading: boolean
}

interface Emits {
  (e: 'post-select', postId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const mapContainer = ref<HTMLElement>()
const geocoding = ref(false)
const error = ref<string | null>(null)

let map: L.Map | null = null
let markerClusterGroup: L.MarkerClusterGroup | null = null
const markers: L.Marker[] = []

// 计算有效的帖子（有位置信息的）
const validPosts = computed(() => {
  return props.posts.filter(post => post.last_seen_location && post.last_seen_location.trim())
})

// 创建自定义图标
const createCustomIcon = (post: Post) => {
  const iconHtml = `
    <div class="custom-marker">
      <div class="marker-content">
        <img src="${getFullImageUrl(post.pet?.photo_url || '')}" alt="${post.pet?.name || '宠物'}" />
      </div>
      <div class="marker-pin"></div>
    </div>
  `

  return L.divIcon({
    html: iconHtml,
    className: 'custom-marker-container',
    iconSize: [40, 50],
    iconAnchor: [20, 50],
    popupAnchor: [0, -50]
  })
}

// 初始化地图
const initMap = () => {
  if (!mapContainer.value) return

  try {
    error.value = null

    // 创建地图实例
    map = L.map(mapContainer.value).setView(
      MAP_CONFIG.DEFAULT_CENTER as [number, number],
      MAP_CONFIG.DEFAULT_ZOOM
    )

    // 添加地图图层
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
      maxZoom: MAP_CONFIG.MAX_ZOOM,
      minZoom: MAP_CONFIG.MIN_ZOOM,
    }).addTo(map)

    // 创建聚类组
    markerClusterGroup = L.markerClusterGroup({
      chunkedLoading: true,
      spiderfyOnMaxZoom: true,
      showCoverageOnHover: false,
      zoomToBoundsOnClick: true,
      maxClusterRadius: 50,
      iconCreateFunction: (cluster) => {
        const count = cluster.getChildCount()
        let className = 'marker-cluster-small'

        if (count > 10) {
          className = 'marker-cluster-large'
        } else if (count > 5) {
          className = 'marker-cluster-medium'
        }

        return L.divIcon({
          html: `<div><span>${count}</span></div>`,
          className: `marker-cluster ${className}`,
          iconSize: [40, 40]
        })
      }
    })

    map.addLayer(markerClusterGroup)

    // 加载帖子标记
    loadPostMarkers()

  } catch (err) {
    console.error('地图初始化失败:', err)
    error.value = '地图初始化失败，请稍后重试'
  }
}

// 清除所有标记
const clearMarkers = () => {
  if (markerClusterGroup) {
    markerClusterGroup.clearLayers()
  }
  markers.length = 0
}

// 地理编码：将地址转换为坐标
const geocodeAddress = async (address: string): Promise<{ lat: number; lng: number } | null> => {
  try {
    // 首先尝试原始地址
    let searchQuery = address
    let url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery)}&limit=1`

    console.log(`地理编码请求: ${searchQuery}`)

    let response = await fetch(url)

    if (!response.ok) {
      console.error(`地理编码API请求失败: ${response.status} ${response.statusText}`)
      return null
    }

    let data = await response.json()
    console.log(`地理编码响应:`, data)

    // 如果原始地址没有结果，且地址包含香港相关信息，尝试添加Hong Kong
    if ((!data || data.length === 0) && (address.includes('香港') || address.includes('新界') || address.includes('九龙') || address.includes('港島'))) {
      searchQuery = `${address}, Hong Kong`
      url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery)}&limit=1`

      console.log(`重试地理编码请求: ${searchQuery}`)

      response = await fetch(url)
      if (response.ok) {
        data = await response.json()
        console.log(`重试地理编码响应:`, data)
      }
    }

    // 如果还是没有结果，尝试提取关键地名
    if ((!data || data.length === 0)) {
      const locationKeywords = extractLocationKeywords(address)
      if (locationKeywords) {
        searchQuery = locationKeywords
        url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery)}&limit=1`

        console.log(`使用关键词重试: ${searchQuery}`)

        response = await fetch(url)
        if (response.ok) {
          data = await response.json()
          console.log(`关键词地理编码响应:`, data)
        }
      }
    }

    if (data && data.length > 0) {
      const result = data[0]
      const coords = {
        lat: parseFloat(result.lat),
        lng: parseFloat(result.lon)
      }
      console.log(`地理编码成功: ${address} -> ${coords.lat}, ${coords.lng}`)
      return coords
    } else {
      console.warn(`地理编码无结果: ${address}`)
      return null
    }
  } catch (error) {
    console.error(`地理编码异常: ${address}`, error)
    return null
  }
}

// 提取地址中的关键位置信息
const extractLocationKeywords = (address: string): string | null => {
  // 香港地区关键词
  const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']

  for (const keyword of hkKeywords) {
    if (address.includes(keyword)) {
      return `${keyword}, Hong Kong`
    }
  }

  // 中国大陆城市关键词
  const chinaKeywords = ['北京', '上海', '广州', '深圳', '中山', '江门', '开平']

  for (const keyword of chinaKeywords) {
    if (address.includes(keyword)) {
      return `${keyword}, China`
    }
  }

  // 其他国家
  if (address.includes('越南')) {
    return 'Vietnam'
  }

  return null
}

// 创建弹窗内容
const createPopupContent = (post: Post) => {
  const petPhoto = post.pet?.photo_url ? getFullImageUrl(post.pet.photo_url) : ''
  const lastSeenTime = new Date(post.last_seen_time).toLocaleString('zh-CN')

  return `
    <div class="post-popup">
      <div class="popup-header">
        ${petPhoto ? `<img src="${petPhoto}" alt="${post.pet?.name || '宠物'}" class="popup-image" />` : ''}
        <div class="popup-info">
          <h3 class="popup-title">${post.pet?.name || '未命名宠物'}</h3>
          <p class="popup-species">${post.pet?.species || ''} · ${post.pet?.color || ''}</p>
        </div>
      </div>
      <div class="popup-details">
        <p class="popup-location"><strong>走失地点：</strong>${post.last_seen_location}</p>
        <p class="popup-time"><strong>走失时间：</strong>${lastSeenTime}</p>
        <p class="popup-owner"><strong>主人：</strong>${post.owner?.username || '匿名'}</p>
        ${post.pet?.description ? `<p class="popup-description"><strong>描述：</strong>${post.pet.description}</p>` : ''}
      </div>
      <div class="popup-actions">
        <button class="popup-btn" onclick="window.postMapSelectPost(${post.id})">查看详情</button>
      </div>
    </div>
  `
}

// 加载帖子标记
const loadPostMarkers = async () => {
  console.log('开始加载帖子标记...')
  console.log('地图实例:', map)
  console.log('聚类组:', markerClusterGroup)
  console.log('有效帖子数量:', validPosts.value.length)
  console.log('有效帖子:', validPosts.value)

  if (!map || !markerClusterGroup) {
    console.error('地图或聚类组未初始化')
    return
  }

  if (validPosts.value.length === 0) {
    console.warn('没有有效的帖子数据')
    return
  }

  geocoding.value = true
  clearMarkers()

  const bounds = L.latLngBounds([])
  let successCount = 0
  let failureCount = 0

  for (const post of validPosts.value) {
    console.log(`处理帖子 ${post.id}: ${post.last_seen_location}`)

    try {
      const coords = await geocodeAddress(post.last_seen_location)
      console.log(`地理编码结果:`, coords)

      if (coords) {
        // 创建标记
        const marker = L.marker([coords.lat, coords.lng], {
          icon: createCustomIcon(post)
        })

        // 添加弹窗
        const popupContent = createPopupContent(post)
        marker.bindPopup(popupContent, {
          maxWidth: 300,
          className: 'custom-popup'
        })

        // 点击事件
        marker.on('click', () => {
          emit('post-select', post.id)
        })

        // 添加到聚类组
        markerClusterGroup.addLayer(marker)
        markers.push(marker)
        bounds.extend([coords.lat, coords.lng])
        successCount++
        console.log(`成功添加标记 ${post.id}`)
      } else {
        failureCount++
        console.warn(`地理编码失败 - ${post.last_seen_location}`)
      }
    } catch (error) {
      failureCount++
      console.error(`处理帖子 ${post.id} 时出错:`, error)
    }
  }

  console.log(`标记加载完成: 成功 ${successCount}, 失败 ${failureCount}`)

  // 调整地图视图以显示所有标记
  if (successCount > 0 && bounds.isValid()) {
    console.log('调整地图视图以显示所有标记')
    map.fitBounds(bounds, { padding: [20, 20] })
  } else {
    console.warn('没有成功的标记，无法调整地图视图')
  }

  geocoding.value = false
}

// 全局函数，供弹窗按钮调用
;(window as any).postMapSelectPost = (postId: number) => {
  emit('post-select', postId)
}

// 监听帖子变化
watch(
  () => props.posts,
  () => {
    if (map) {
      loadPostMarkers()
    }
  },
  { deep: true }
)

// 生命周期
onMounted(() => {
  initMap()
})

onUnmounted(() => {
  if (map) {
    map.remove()
    map = null
  }
  clearMarkers()
  // 清理全局函数
  delete (window as any).postMapSelectPost
})
</script>

<style scoped>
/* 地图容器样式 */
.relative {
  position: relative;
}

/* 自定义标记样式 */
:deep(.custom-marker-container) {
  background: transparent;
  border: none;
}

:deep(.custom-marker) {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

:deep(.marker-content) {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 3px solid #ef4444;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

:deep(.marker-content img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

:deep(.marker-pin) {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 12px solid #ef4444;
  margin-top: -2px;
}

/* 弹窗样式 */
:deep(.custom-popup .leaflet-popup-content-wrapper) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.custom-popup .leaflet-popup-content) {
  margin: 0;
  padding: 0;
  width: 280px !important;
}

:deep(.post-popup) {
  font-family: inherit;
}

:deep(.popup-header) {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.popup-image) {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  object-fit: cover;
  margin-right: 12px;
  border: 2px solid #e5e7eb;
}

:deep(.popup-info) {
  flex: 1;
}

:deep(.popup-title) {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

:deep(.popup-species) {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

:deep(.popup-details) {
  padding: 12px;
}

:deep(.popup-details p) {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #374151;
  line-height: 1.4;
}

:deep(.popup-details p:last-child) {
  margin-bottom: 0;
}

:deep(.popup-details strong) {
  color: #111827;
  font-weight: 500;
}

:deep(.popup-actions) {
  padding: 12px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

:deep(.popup-btn) {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.popup-btn:hover) {
  background: #2563eb;
}

/* 聚类样式 */
:deep(.marker-cluster) {
  background-clip: padding-box;
  border-radius: 20px;
}

:deep(.marker-cluster div) {
  width: 30px;
  height: 30px;
  margin-left: 5px;
  margin-top: 5px;
  text-align: center;
  border-radius: 15px;
  font: 12px "Helvetica Neue", Arial, Helvetica, sans-serif;
  font-weight: bold;
}

:deep(.marker-cluster span) {
  line-height: 30px;
  color: white;
}

:deep(.marker-cluster-small) {
  background-color: rgba(181, 226, 140, 0.6);
}

:deep(.marker-cluster-small div) {
  background-color: rgba(110, 204, 57, 0.8);
}

:deep(.marker-cluster-medium) {
  background-color: rgba(241, 211, 87, 0.6);
}

:deep(.marker-cluster-medium div) {
  background-color: rgba(240, 194, 12, 0.8);
}

:deep(.marker-cluster-large) {
  background-color: rgba(253, 156, 115, 0.6);
}

:deep(.marker-cluster-large div) {
  background-color: rgba(241, 128, 23, 0.8);
}

/* 响应式调整 */
@media (max-width: 640px) {
  :deep(.custom-popup .leaflet-popup-content) {
    width: 240px !important;
  }

  :deep(.popup-header) {
    padding: 10px;
  }

  :deep(.popup-image) {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }

  :deep(.popup-title) {
    font-size: 15px;
  }

  :deep(.popup-species) {
    font-size: 13px;
  }

  :deep(.popup-details) {
    padding: 10px;
  }

  :deep(.popup-details p) {
    font-size: 13px;
  }
}
</style>
