[{"text": "初始化认证状态", "file": "src/App.vue", "line": 10, "context": "// 初始化认证状态"}, {"text": "主要内容", "file": "src/App.vue", "line": 17, "context": "<!-- 主要内容 -->"}, {"text": "移动端底部导航", "file": "src/App.vue", "line": 22, "context": "<!-- 移动端底部导航 -->"}, {"text": "上传区域", "file": "src/components/ImageUpload.vue", "line": 9, "context": "<!-- 上传区域 -->"}, {"text": "上传图标和文字", "file": "src/components/ImageUpload.vue", "line": 26, "context": "<!-- 上传图标和文字 -->"}, {"text": "点击上传", "file": "src/components/ImageUpload.vue", "line": 32, "context": "<span class=\"font-medium text-primary-600\">点击上传</span>"}, {"text": "或拖拽文件到此处", "file": "src/components/ImageUpload.vue", "line": 33, "context": "或拖拽文件到此处"}, {"text": "支持", "file": "src/components/ImageUpload.vue", "line": 36, "context": "支持 {{ acceptText }}，最大 {{ maxSizeMB }}MB"}, {"text": "最大", "file": "src/components/ImageUpload.vue", "line": 36, "context": "支持 {{ acceptText }}，最大 {{ maxSizeMB }}MB"}, {"text": "图片预览", "file": "src/components/ImageUpload.vue", "line": 40, "context": "<!-- 图片预览 -->"}, {"text": "预览图", "file": "src/components/ImageUpload.vue", "line": 49, "context": ":alt=\"`预览图 ${index + 1}`\""}, {"text": "添加更多图片按钮", "file": "src/components/ImageUpload.vue", "line": 61, "context": "<!-- 添加更多图片按钮 -->"}, {"text": "上传进度", "file": "src/components/ImageUpload.vue", "line": 73, "context": "<!-- 上传进度 -->"}, {"text": "上传中", "file": "src/components/ImageUpload.vue", "line": 77, "context": "<p class=\"mt-2 text-sm text-gray-600\">上传中...</p>"}, {"text": "错误提示", "file": "src/components/ImageUpload.vue", "line": 82, "context": "<!-- 错误提示 -->"}, {"text": "帮助文字", "file": "src/components/ImageUpload.vue", "line": 87, "context": "<!-- 帮助文字 -->"}, {"text": "上传图片", "file": "src/components/ImageUpload.vue", "line": 128, "context": "label: '上传图片',"}, {"text": "计算属性", "file": "src/components/ImageUpload.vue", "line": 145, "context": "// 计算属性"}, {"text": "触发文件选择", "file": "src/components/ImageUpload.vue", "line": 151, "context": "// 触发文件选择"}, {"text": "处理文件选择", "file": "src/components/ImageUpload.vue", "line": 156, "context": "// 处理文件选择"}, {"text": "处理拖拽上传", "file": "src/components/ImageUpload.vue", "line": 165, "context": "// 处理拖拽上传"}, {"text": "处理文件", "file": "src/components/ImageUpload.vue", "line": 173, "context": "// 处理文件"}, {"text": "验证文件数量", "file": "src/components/ImageUpload.vue", "line": 177, "context": "// 验证文件数量"}, {"text": "只能上传一个文件", "file": "src/components/ImageUpload.vue", "line": 179, "context": "error.value = '只能上传一个文件'"}, {"text": "最多只能上传", "file": "src/components/ImageUpload.vue", "line": 184, "context": "error.value = `最多只能上传 ${props.maxFiles} 个文件`"}, {"text": "个文件", "file": "src/components/ImageUpload.vue", "line": 184, "context": "error.value = `最多只能上传 ${props.maxFiles} 个文件`"}, {"text": "验证文件类型", "file": "src/components/ImageUpload.vue", "line": 194, "context": "// 验证文件类型"}, {"text": "文件", "file": "src/components/ImageUpload.vue", "line": 196, "context": "error.value = `文件 ${file.name} 不是有效的图片格式`"}, {"text": "不是有效的图片格式", "file": "src/components/ImageUpload.vue", "line": 196, "context": "error.value = `文件 ${file.name} 不是有效的图片格式`"}, {"text": "验证文件大小", "file": "src/components/ImageUpload.vue", "line": 200, "context": "// 验证文件大小"}, {"text": "文件", "file": "src/components/ImageUpload.vue", "line": 202, "context": "error.value = `文件 ${file.name} 大小超过 ${props.maxSizeMB}MB`"}, {"text": "大小超过", "file": "src/components/ImageUpload.vue", "line": 202, "context": "error.value = `文件 ${file.name} 大小超过 ${props.maxSizeMB}MB`"}, {"text": "压缩图片", "file": "src/components/ImageUpload.vue", "line": 206, "context": "// 压缩图片"}, {"text": "创建预览", "file": "src/components/ImageUpload.vue", "line": 219, "context": "// 创建预览"}, {"text": "清理旧预览", "file": "src/components/ImageUpload.vue", "line": 228, "context": "// 清理旧预览"}, {"text": "更新值", "file": "src/components/ImageUpload.vue", "line": 235, "context": "// 更新值"}, {"text": "文件处理失败", "file": "src/components/ImageUpload.vue", "line": 239, "context": "console.error('文件处理失败:', err)"}, {"text": "文件处理失败", "file": "src/components/ImageUpload.vue", "line": 240, "context": "error.value = '文件处理失败，请重试'"}, {"text": "请重试", "file": "src/components/ImageUpload.vue", "line": 240, "context": "error.value = '文件处理失败，请重试'"}, {"text": "移除图片", "file": "src/components/ImageUpload.vue", "line": 246, "context": "// 移除图片"}, {"text": "更新值", "file": "src/components/ImageUpload.vue", "line": 254, "context": "// 更新值"}, {"text": "监听", "file": "src/components/ImageUpload.vue", "line": 265, "context": "// 监听props变化"}, {"text": "变化", "file": "src/components/ImageUpload.vue", "line": 265, "context": "// 监听props变化"}, {"text": "清理预览", "file": "src/components/ImageUpload.vue", "line": 270, "context": "// 清理预览"}, {"text": "地址输入框", "file": "src/components/MapPicker.vue", "line": 8, "context": "<!-- 地址输入框 -->"}, {"text": "搜索地址", "file": "src/components/MapPicker.vue", "line": 22, "context": "搜索地址"}, {"text": "地图容器", "file": "src/components/MapPicker.vue", "line": 26, "context": "<!-- 地图容器 -->"}, {"text": "选中的位置信息", "file": "src/components/MapPicker.vue", "line": 32, "context": "<!-- 选中的位置信息 -->"}, {"text": "选中位置", "file": "src/components/MapPicker.vue", "line": 35, "context": "<strong>选中位置：</strong>"}, {"text": "纬度", "file": "src/components/MapPicker.vue", "line": 38, "context": "纬度: {{ selectedLocation.lat.toFixed(6) }},"}, {"text": "经度", "file": "src/components/MapPicker.vue", "line": 39, "context": "经度: {{ selectedLocation.lng.toFixed(6) }}"}, {"text": "地址", "file": "src/components/MapPicker.vue", "line": 42, "context": "地址: {{ selectedLocation.address }}"}, {"text": "修复", "file": "src/components/MapPicker.vue", "line": 55, "context": "// 修复Leaflet图标问题"}, {"text": "图标问题", "file": "src/components/MapPicker.vue", "line": 55, "context": "// 修复Leaflet图标问题"}, {"text": "选择位置", "file": "src/components/MapPicker.vue", "line": 75, "context": "label: '选择位置',"}, {"text": "输入地址搜索", "file": "src/components/MapPicker.vue", "line": 76, "context": "placeholder: '输入地址搜索...',"}, {"text": "初始化地图", "file": "src/components/MapPicker.vue", "line": 89, "context": "// 初始化地图"}, {"text": "创建地图实例", "file": "src/components/MapPicker.vue", "line": 93, "context": "// 创建地图实例"}, {"text": "添加地图图层", "file": "src/components/MapPicker.vue", "line": 99, "context": "// 添加地图图层"}, {"text": "地图点击事件", "file": "src/components/MapPicker.vue", "line": 106, "context": "// 地图点击事件"}, {"text": "如果有初始值", "file": "src/components/MapPicker.vue", "line": 112, "context": "// 如果有初始值，设置标记"}, {"text": "设置标记", "file": "src/components/MapPicker.vue", "line": 112, "context": "// 如果有初始值，设置标记"}, {"text": "设置位置", "file": "src/components/MapPicker.vue", "line": 118, "context": "// 设置位置"}, {"text": "移除旧标记", "file": "src/components/MapPicker.vue", "line": 124, "context": "// 移除旧标记"}, {"text": "添加新标记", "file": "src/components/MapPicker.vue", "line": 129, "context": "// 添加新标记"}, {"text": "移动地图视图到选中位置", "file": "src/components/MapPicker.vue", "line": 132, "context": "// 移动地图视图到选中位置"}, {"text": "尝试获取地址信息", "file": "src/components/MapPicker.vue", "line": 135, "context": "// 尝试获取地址信息（反向地理编码）"}, {"text": "反向地理编码", "file": "src/components/MapPicker.vue", "line": 135, "context": "// 尝试获取地址信息（反向地理编码）"}, {"text": "触发更新事件", "file": "src/components/MapPicker.vue", "line": 138, "context": "// 触发更新事件"}, {"text": "搜索地址", "file": "src/components/MapPicker.vue", "line": 142, "context": "// 搜索地址"}, {"text": "使用", "file": "src/components/MapPicker.vue", "line": 147, "context": "// 使用Nominatim API进行地理编码"}, {"text": "进行地理编码", "file": "src/components/MapPicker.vue", "line": 147, "context": "// 使用Nominatim API进行地理编码"}, {"text": "未找到该地址", "file": "src/components/MapPicker.vue", "line": 166, "context": "alert('未找到该地址，请尝试其他关键词')"}, {"text": "请尝试其他关键词", "file": "src/components/MapPicker.vue", "line": 166, "context": "alert('未找到该地址，请尝试其他关键词')"}, {"text": "地址搜索失败", "file": "src/components/MapPicker.vue", "line": 169, "context": "console.error('地址搜索失败:', error)"}, {"text": "地址搜索失败", "file": "src/components/MapPicker.vue", "line": 170, "context": "alert('地址搜索失败，请稍后重试')"}, {"text": "请稍后重试", "file": "src/components/MapPicker.vue", "line": 170, "context": "alert('地址搜索失败，请稍后重试')"}, {"text": "反向地理编码", "file": "src/components/MapPicker.vue", "line": 174, "context": "// 反向地理编码"}, {"text": "反向地理编码失败", "file": "src/components/MapPicker.vue", "line": 190, "context": "console.error('反向地理编码失败:', error)"}, {"text": "重新调整地图大小", "file": "src/components/MapPicker.vue", "line": 194, "context": "// 重新调整地图大小"}, {"text": "使用", "file": "src/components/MapPicker.vue", "line": 197, "context": "// 使用 setTimeout 确保DOM已经更新"}, {"text": "确保", "file": "src/components/MapPicker.vue", "line": 197, "context": "// 使用 setTimeout 确保DOM已经更新"}, {"text": "已经更新", "file": "src/components/MapPicker.vue", "line": 197, "context": "// 使用 setTimeout 确保DOM已经更新"}, {"text": "监听", "file": "src/components/MapPicker.vue", "line": 204, "context": "// 监听props变化"}, {"text": "变化", "file": "src/components/MapPicker.vue", "line": 204, "context": "// 监听props变化"}, {"text": "监听容器可见性变化", "file": "src/components/MapPicker.vue", "line": 215, "context": "// 监听容器可见性变化"}, {"text": "延迟调整地图大小", "file": "src/components/MapPicker.vue", "line": 227, "context": "// 延迟调整地图大小，确保容器已完全渲染"}, {"text": "确保容器已完全渲染", "file": "src/components/MapPicker.vue", "line": 227, "context": "// 延迟调整地图大小，确保容器已完全渲染"}, {"text": "首页", "file": "src/components/MobileNav.vue", "line": 14, "context": "<span>首页</span>"}, {"text": "浏览", "file": "src/components/MobileNav.vue", "line": 27, "context": "<span>浏览</span>"}, {"text": "发布", "file": "src/components/MobileNav.vue", "line": 41, "context": "<span>发布</span>"}, {"text": "我的", "file": "src/components/MobileNav.vue", "line": 55, "context": "<span>我的</span>"}, {"text": "登录", "file": "src/components/MobileNav.vue", "line": 69, "context": "<span>登录</span>"}, {"text": "移动端分页", "file": "src/components/Pagination.vue", "line": 4, "context": "<!-- 移动端分页 -->"}, {"text": "上一页", "file": "src/components/Pagination.vue", "line": 10, "context": "上一页"}, {"text": "下一页", "file": "src/components/Pagination.vue", "line": 17, "context": "下一页"}, {"text": "显示第", "file": "src/components/Pagination.vue", "line": 24, "context": "显示第"}, {"text": "条结果", "file": "src/components/Pagination.vue", "line": 30, "context": "条结果"}, {"text": "上一页", "file": "src/components/Pagination.vue", "line": 36, "context": "<!-- 上一页 -->"}, {"text": "上一页", "file": "src/components/Pagination.vue", "line": 42, "context": "<span class=\"sr-only\">上一页</span>"}, {"text": "页码", "file": "src/components/Pagination.vue", "line": 46, "context": "<!-- 页码 -->"}, {"text": "下一页", "file": "src/components/Pagination.vue", "line": 68, "context": "<!-- 下一页 -->"}, {"text": "下一页", "file": "src/components/Pagination.vue", "line": 74, "context": "<span class=\"sr-only\">下一页</span>"}, {"text": "计算属性", "file": "src/components/Pagination.vue", "line": 104, "context": "// 计算属性"}, {"text": "如果总页数小于等于", "file": "src/components/Pagination.vue", "line": 118, "context": "// 如果总页数小于等于7，显示所有页码"}, {"text": "显示所有页码", "file": "src/components/Pagination.vue", "line": 118, "context": "// 如果总页数小于等于7，显示所有页码"}, {"text": "总是显示第一页", "file": "src/components/Pagination.vue", "line": 123, "context": "// 总是显示第一页"}, {"text": "当前页在前面", "file": "src/components/Pagination.vue", "line": 127, "context": "// 当前页在前面"}, {"text": "当前页在后面", "file": "src/components/Pagination.vue", "line": 134, "context": "// 当前页在后面"}, {"text": "当前页在中间", "file": "src/components/Pagination.vue", "line": 140, "context": "// 当前页在中间"}, {"text": "模态框标题", "file": "src/components/PetDetailModal.vue", "line": 8, "context": "<!-- 模态框标题 -->"}, {"text": "宠物详情", "file": "src/components/PetDetailModal.vue", "line": 11, "context": "宠物详情"}, {"text": "错误提示", "file": "src/components/PetDetailModal.vue", "line": 23, "context": "<!-- 错误提示 -->"}, {"text": "操作失败", "file": "src/components/PetDetailModal.vue", "line": 28, "context": "<h3 class=\"text-sm font-medium text-red-800\">操作失败</h3>"}, {"text": "宠物信息展示", "file": "src/components/PetDetailModal.vue", "line": 34, "context": "<!-- 宠物信息展示 -->"}, {"text": "宠物照片", "file": "src/components/PetDetailModal.vue", "line": 36, "context": "<!-- 宠物照片 -->"}, {"text": "基本信息", "file": "src/components/PetDetailModal.vue", "line": 55, "context": "<!-- 基本信息 -->"}, {"text": "宠物名称", "file": "src/components/PetDetailModal.vue", "line": 58, "context": "<label class=\"block text-sm font-medium text-gray-700\">宠物名称</label>"}, {"text": "未命名", "file": "src/components/PetDetailModal.vue", "line": 59, "context": "<p class=\"mt-1 text-sm text-gray-900\">{{ pet.name || '未命名' }}</p>"}, {"text": "物种", "file": "src/components/PetDetailModal.vue", "line": 63, "context": "<label class=\"block text-sm font-medium text-gray-700\">物种</label>"}, {"text": "品种", "file": "src/components/PetDetailModal.vue", "line": 68, "context": "<label class=\"block text-sm font-medium text-gray-700\">品种</label>"}, {"text": "未知", "file": "src/components/PetDetailModal.vue", "line": 69, "context": "<p class=\"mt-1 text-sm text-gray-900\">{{ pet.breed || '未知' }}</p>"}, {"text": "颜色", "file": "src/components/PetDetailModal.vue", "line": 73, "context": "<label class=\"block text-sm font-medium text-gray-700\">颜色</label>"}, {"text": "性别", "file": "src/components/PetDetailModal.vue", "line": 78, "context": "<label class=\"block text-sm font-medium text-gray-700\">性别</label>"}, {"text": "年龄", "file": "src/components/PetDetailModal.vue", "line": 83, "context": "<label class=\"block text-sm font-medium text-gray-700\">年龄</label>"}, {"text": "未知", "file": "src/components/PetDetailModal.vue", "line": 84, "context": "<p class=\"mt-1 text-sm text-gray-900\">{{ pet.age ? `${pet.age}岁` : '未知' }}</p>"}, {"text": "描述", "file": "src/components/PetDetailModal.vue", "line": 88, "context": "<!-- 描述 -->"}, {"text": "描述", "file": "src/components/PetDetailModal.vue", "line": 90, "context": "<label class=\"block text-sm font-medium text-gray-700\">描述</label>"}, {"text": "创建时间", "file": "src/components/PetDetailModal.vue", "line": 94, "context": "<!-- 创建时间 -->"}, {"text": "添加时间", "file": "src/components/PetDetailModal.vue", "line": 96, "context": "<label class=\"block text-sm font-medium text-gray-700\">添加时间</label>"}, {"text": "操作按钮", "file": "src/components/PetDetailModal.vue", "line": 101, "context": "<!-- 操作按钮 -->"}, {"text": "删除中", "file": "src/components/PetDetailModal.vue", "line": 113, "context": "删除中..."}, {"text": "删除宠物", "file": "src/components/PetDetailModal.vue", "line": 115, "context": "<span v-else>删除宠物</span>"}, {"text": "关闭", "file": "src/components/PetDetailModal.vue", "line": 124, "context": "关闭"}, {"text": "编辑信息", "file": "src/components/PetDetailModal.vue", "line": 131, "context": "编辑信息"}, {"text": "编辑表单模态框", "file": "src/components/PetDetailModal.vue", "line": 137, "context": "<!-- 编辑表单模态框 -->"}, {"text": "删除确认模态框", "file": "src/components/PetDetailModal.vue", "line": 146, "context": "<!-- 删除确认模态框 -->"}, {"text": "确认删除", "file": "src/components/PetDetailModal.vue", "line": 155, "context": "<h3 class=\"text-lg font-medium text-gray-900 mb-2\">确认删除</h3>"}, {"text": "确定要删除宠物", "file": "src/components/PetDetailModal.vue", "line": 157, "context": "确定要删除宠物\"{{ pet.name }}\"吗？此操作无法撤销。"}, {"text": "此操作无法撤销", "file": "src/components/PetDetailModal.vue", "line": 157, "context": "确定要删除宠物\"{{ pet.name }}\"吗？此操作无法撤销。"}, {"text": "取消", "file": "src/components/PetDetailModal.vue", "line": 165, "context": "取消"}, {"text": "删除中", "file": "src/components/PetDetailModal.vue", "line": 177, "context": "删除中..."}, {"text": "确认删除", "file": "src/components/PetDetailModal.vue", "line": 179, "context": "<span v-else>确认删除</span>"}, {"text": "状态", "file": "src/components/PetDetailModal.vue", "line": 212, "context": "// 状态"}, {"text": "获取标签方法", "file": "src/components/PetDetailModal.vue", "line": 219, "context": "// 获取标签方法"}, {"text": "未知", "file": "src/components/PetDetailModal.vue", "line": 232, "context": "return genderOption?.label || '未知'"}, {"text": "处理编辑成功", "file": "src/components/PetDetailModal.vue", "line": 235, "context": "// 处理编辑成功"}, {"text": "处理删除", "file": "src/components/PetDetailModal.vue", "line": 241, "context": "// 处理删除"}, {"text": "确认删除", "file": "src/components/PetDetailModal.vue", "line": 246, "context": "// 确认删除"}, {"text": "删除失败", "file": "src/components/PetDetailModal.vue", "line": 259, "context": "throw new Error(response.message || '删除失败')"}, {"text": "删除宠物失败", "file": "src/components/PetDetailModal.vue", "line": 262, "context": "console.error('删除宠物失败:', err)"}, {"text": "删除失败", "file": "src/components/PetDetailModal.vue", "line": 263, "context": "error.value = err instanceof Error ? err.message : '删除失败，请稍后重试'"}, {"text": "请稍后重试", "file": "src/components/PetDetailModal.vue", "line": 263, "context": "error.value = err instanceof Error ? err.message : '删除失败，请稍后重试'"}, {"text": "模态框标题", "file": "src/components/PetForm.vue", "line": 8, "context": "<!-- 模态框标题 -->"}, {"text": "编辑宠物信息", "file": "src/components/PetForm.vue", "line": 11, "context": "{{ isEdit ? '编辑宠物信息' : '添加宠物' }}"}, {"text": "添加宠物", "file": "src/components/PetForm.vue", "line": 11, "context": "{{ isEdit ? '编辑宠物信息' : '添加宠物' }}"}, {"text": "错误提示", "file": "src/components/PetForm.vue", "line": 23, "context": "<!-- 错误提示 -->"}, {"text": "操作失败", "file": "src/components/PetForm.vue", "line": 28, "context": "<h3 class=\"text-sm font-medium text-red-800\">操作失败</h3>"}, {"text": "表单", "file": "src/components/PetForm.vue", "line": 34, "context": "<!-- 表单 -->"}, {"text": "宠物照片", "file": "src/components/PetForm.vue", "line": 36, "context": "<!-- 宠物照片 -->"}, {"text": "宠物照片", "file": "src/components/PetForm.vue", "line": 39, "context": "宠物照片"}, {"text": "基本信息", "file": "src/components/PetForm.vue", "line": 50, "context": "<!-- 基本信息 -->"}, {"text": "宠物名称", "file": "src/components/PetForm.vue", "line": 52, "context": "<!-- 宠物名称 -->"}, {"text": "宠物名称", "file": "src/components/PetForm.vue", "line": 55, "context": "宠物名称 <span class=\"text-red-500\">*</span>"}, {"text": "请输入宠物名称", "file": "src/components/PetForm.vue", "line": 63, "context": "placeholder=\"请输入宠物名称\""}, {"text": "物种", "file": "src/components/PetForm.vue", "line": 67, "context": "<!-- 物种 -->"}, {"text": "物种", "file": "src/components/PetForm.vue", "line": 70, "context": "物种 <span class=\"text-red-500\">*</span>"}, {"text": "请选择物种", "file": "src/components/PetForm.vue", "line": 78, "context": "<option value=\"\">请选择物种</option>"}, {"text": "品种", "file": "src/components/PetForm.vue", "line": 89, "context": "<!-- 品种 -->"}, {"text": "品种", "file": "src/components/PetForm.vue", "line": 92, "context": "品种"}, {"text": "请输入品种", "file": "src/components/PetForm.vue", "line": 99, "context": "placeholder=\"请输入品种（可选）\""}, {"text": "可选", "file": "src/components/PetForm.vue", "line": 99, "context": "placeholder=\"请输入品种（可选）\""}, {"text": "颜色", "file": "src/components/PetForm.vue", "line": 103, "context": "<!-- 颜色 -->"}, {"text": "颜色", "file": "src/components/PetForm.vue", "line": 106, "context": "颜色 <span class=\"text-red-500\">*</span>"}, {"text": "请选择颜色", "file": "src/components/PetForm.vue", "line": 114, "context": "<option value=\"\">请选择颜色</option>"}, {"text": "性别", "file": "src/components/PetForm.vue", "line": 125, "context": "<!-- 性别 -->"}, {"text": "性别", "file": "src/components/PetForm.vue", "line": 128, "context": "性别 <span class=\"text-red-500\">*</span>"}, {"text": "年龄", "file": "src/components/PetForm.vue", "line": 146, "context": "<!-- 年龄 -->"}, {"text": "年龄", "file": "src/components/PetForm.vue", "line": 149, "context": "年龄"}, {"text": "请输入年龄", "file": "src/components/PetForm.vue", "line": 158, "context": "placeholder=\"请输入年龄（可选）\""}, {"text": "可选", "file": "src/components/PetForm.vue", "line": 158, "context": "placeholder=\"请输入年龄（可选）\""}, {"text": "描述", "file": "src/components/PetForm.vue", "line": 163, "context": "<!-- 描述 -->"}, {"text": "描述", "file": "src/components/PetForm.vue", "line": 166, "context": "描述"}, {"text": "请输入宠物的特征描述", "file": "src/components/PetForm.vue", "line": 173, "context": "placeholder=\"请输入宠物的特征描述（可选）\""}, {"text": "可选", "file": "src/components/PetForm.vue", "line": 173, "context": "placeholder=\"请输入宠物的特征描述（可选）\""}, {"text": "操作按钮", "file": "src/components/PetForm.vue", "line": 177, "context": "<!-- 操作按钮 -->"}, {"text": "取消", "file": "src/components/PetForm.vue", "line": 185, "context": "取消"}, {"text": "更新中", "file": "src/components/PetForm.vue", "line": 197, "context": "{{ isEdit ? '更新中...' : '添加中...' }}"}, {"text": "添加中", "file": "src/components/PetForm.vue", "line": 197, "context": "{{ isEdit ? '更新中...' : '添加中...' }}"}, {"text": "更新宠物", "file": "src/components/PetForm.vue", "line": 200, "context": "{{ isEdit ? '更新宠物' : '添加宠物' }}"}, {"text": "添加宠物", "file": "src/components/PetForm.vue", "line": 200, "context": "{{ isEdit ? '更新宠物' : '添加宠物' }}"}, {"text": "状态", "file": "src/components/PetForm.vue", "line": 234, "context": "// 状态"}, {"text": "表单数据", "file": "src/components/PetForm.vue", "line": 238, "context": "// 表单数据"}, {"text": "计算属性", "file": "src/components/PetForm.vue", "line": 250, "context": "// 计算属性"}, {"text": "监听", "file": "src/components/PetForm.vue", "line": 266, "context": "// 监听pet变化，初始化表单"}, {"text": "变化", "file": "src/components/PetForm.vue", "line": 266, "context": "// 监听pet变化，初始化表单"}, {"text": "初始化表单", "file": "src/components/PetForm.vue", "line": 266, "context": "// 监听pet变化，初始化表单"}, {"text": "重置表单", "file": "src/components/PetForm.vue", "line": 280, "context": "// 重置表单"}, {"text": "处理图片错误", "file": "src/components/PetForm.vue", "line": 295, "context": "// 处理图片错误"}, {"text": "提交表单", "file": "src/components/PetForm.vue", "line": 300, "context": "// 提交表单"}, {"text": "操作失败", "file": "src/components/PetForm.vue", "line": 318, "context": "throw new Error(response.message || '操作失败')"}, {"text": "宠物操作失败", "file": "src/components/PetForm.vue", "line": 321, "context": "console.error('宠物操作失败:', err)"}, {"text": "操作失败", "file": "src/components/PetForm.vue", "line": 322, "context": "error.value = err instanceof Error ? err.message : '操作失败，请稍后重试'"}, {"text": "请稍后重试", "file": "src/components/PetForm.vue", "line": 322, "context": "error.value = err instanceof Error ? err.message : '操作失败，请稍后重试'"}, {"text": "地图容器", "file": "src/components/PostsMap.vue", "line": 3, "context": "<!-- 地图容器 -->"}, {"text": "加载状态", "file": "src/components/PostsMap.vue", "line": 9, "context": "<!-- 加载状态 -->"}, {"text": "正在定位地址", "file": "src/components/PostsMap.vue", "line": 17, "context": "{{ geocoding ? '正在定位地址...' : '加载地图中...' }}"}, {"text": "加载地图中", "file": "src/components/PostsMap.vue", "line": 17, "context": "{{ geocoding ? '正在定位地址...' : '加载地图中...' }}"}, {"text": "地图信息面板", "file": "src/components/PostsMap.vue", "line": 22, "context": "<!-- 地图信息面板 -->"}, {"text": "走失位置", "file": "src/components/PostsMap.vue", "line": 27, "context": "<span>走失位置</span>"}, {"text": "找到", "file": "src/components/PostsMap.vue", "line": 30, "context": "找到 {{ validPosts.length }} 个走失信息"}, {"text": "个走失信息", "file": "src/components/PostsMap.vue", "line": 30, "context": "找到 {{ validPosts.length }} 个走失信息"}, {"text": "错误提示", "file": "src/components/PostsMap.vue", "line": 35, "context": "<!-- 错误提示 -->"}, {"text": "地图加载失败", "file": "src/components/PostsMap.vue", "line": 44, "context": "<h3 class=\"mt-2 text-sm font-medium text-gray-900\">地图加载失败</h3>"}, {"text": "重新加载", "file": "src/components/PostsMap.vue", "line": 50, "context": "重新加载"}, {"text": "修复", "file": "src/components/PostsMap.vue", "line": 67, "context": "// 修复Leaflet图标问题"}, {"text": "图标问题", "file": "src/components/PostsMap.vue", "line": 67, "context": "// 修复Leaflet图标问题"}, {"text": "计算有效的帖子", "file": "src/components/PostsMap.vue", "line": 95, "context": "// 计算有效的帖子（有位置信息的）"}, {"text": "有位置信息的", "file": "src/components/PostsMap.vue", "line": 95, "context": "// 计算有效的帖子（有位置信息的）"}, {"text": "创建自定义图标", "file": "src/components/PostsMap.vue", "line": 100, "context": "// 创建自定义图标"}, {"text": "宠物", "file": "src/components/PostsMap.vue", "line": 105, "context": "<img src=\"${getFullImageUrl(post.pet?.photo_url || '')}\" alt=\"${post.pet?.name || '宠物'}\" />"}, {"text": "初始化地图", "file": "src/components/PostsMap.vue", "line": 120, "context": "// 初始化地图"}, {"text": "创建地图实例", "file": "src/components/PostsMap.vue", "line": 127, "context": "// 创建地图实例"}, {"text": "添加地图图层", "file": "src/components/PostsMap.vue", "line": 133, "context": "// 添加地图图层"}, {"text": "创建聚类组", "file": "src/components/PostsMap.vue", "line": 140, "context": "// 创建聚类组"}, {"text": "加载帖子标记", "file": "src/components/PostsMap.vue", "line": 167, "context": "// 加载帖子标记"}, {"text": "地图初始化失败", "file": "src/components/PostsMap.vue", "line": 171, "context": "console.error('地图初始化失败:', err)"}, {"text": "地图初始化失败", "file": "src/components/PostsMap.vue", "line": 172, "context": "error.value = '地图初始化失败，请稍后重试'"}, {"text": "请稍后重试", "file": "src/components/PostsMap.vue", "line": 172, "context": "error.value = '地图初始化失败，请稍后重试'"}, {"text": "清除所有标记", "file": "src/components/PostsMap.vue", "line": 176, "context": "// 清除所有标记"}, {"text": "地理编码", "file": "src/components/PostsMap.vue", "line": 184, "context": "// 地理编码：将地址转换为坐标"}, {"text": "将地址转换为坐标", "file": "src/components/PostsMap.vue", "line": 184, "context": "// 地理编码：将地址转换为坐标"}, {"text": "首先尝试原始地址", "file": "src/components/PostsMap.vue", "line": 187, "context": "// 首先尝试原始地址"}, {"text": "地理编码请求", "file": "src/components/PostsMap.vue", "line": 191, "context": "console.log(`地理编码请求: ${searchQuery}`)"}, {"text": "地理编码", "file": "src/components/PostsMap.vue", "line": 196, "context": "console.error(`地理编码API请求失败: ${response.status} ${response.statusText}`)"}, {"text": "请求失败", "file": "src/components/PostsMap.vue", "line": 196, "context": "console.error(`地理编码API请求失败: ${response.status} ${response.statusText}`)"}, {"text": "地理编码响应", "file": "src/components/PostsMap.vue", "line": 201, "context": "console.log(`地理编码响应:`, data)"}, {"text": "如果原始地址没有结果", "file": "src/components/PostsMap.vue", "line": 203, "context": "// 如果原始地址没有结果，且地址包含香港相关信息，尝试添加Hong Kong"}, {"text": "且地址包含香港相关信息", "file": "src/components/PostsMap.vue", "line": 203, "context": "// 如果原始地址没有结果，且地址包含香港相关信息，尝试添加Hong Kong"}, {"text": "尝试添加", "file": "src/components/PostsMap.vue", "line": 203, "context": "// 如果原始地址没有结果，且地址包含香港相关信息，尝试添加Hong Kong"}, {"text": "香港", "file": "src/components/PostsMap.vue", "line": 204, "context": "if ((!data || data.length === 0) && (address.includes('香港') || address.includes('新界') || address.includes('九龙') || address.includes('港島'))) {"}, {"text": "新界", "file": "src/components/PostsMap.vue", "line": 204, "context": "if ((!data || data.length === 0) && (address.includes('香港') || address.includes('新界') || address.includes('九龙') || address.includes('港島'))) {"}, {"text": "九龙", "file": "src/components/PostsMap.vue", "line": 204, "context": "if ((!data || data.length === 0) && (address.includes('香港') || address.includes('新界') || address.includes('九龙') || address.includes('港島'))) {"}, {"text": "港島", "file": "src/components/PostsMap.vue", "line": 204, "context": "if ((!data || data.length === 0) && (address.includes('香港') || address.includes('新界') || address.includes('九龙') || address.includes('港島'))) {"}, {"text": "重试地理编码请求", "file": "src/components/PostsMap.vue", "line": 208, "context": "console.log(`重试地理编码请求: ${searchQuery}`)"}, {"text": "重试地理编码响应", "file": "src/components/PostsMap.vue", "line": 213, "context": "console.log(`重试地理编码响应:`, data)"}, {"text": "如果还是没有结果", "file": "src/components/PostsMap.vue", "line": 217, "context": "// 如果还是没有结果，尝试提取关键地名"}, {"text": "尝试提取关键地名", "file": "src/components/PostsMap.vue", "line": 217, "context": "// 如果还是没有结果，尝试提取关键地名"}, {"text": "使用关键词重试", "file": "src/components/PostsMap.vue", "line": 224, "context": "console.log(`使用关键词重试: ${searchQuery}`)"}, {"text": "关键词地理编码响应", "file": "src/components/PostsMap.vue", "line": 229, "context": "console.log(`关键词地理编码响应:`, data)"}, {"text": "地理编码成功", "file": "src/components/PostsMap.vue", "line": 240, "context": "console.log(`地理编码成功: ${address} -> ${coords.lat}, ${coords.lng}`)"}, {"text": "地理编码无结果", "file": "src/components/PostsMap.vue", "line": 243, "context": "console.warn(`地理编码无结果: ${address}`)"}, {"text": "地理编码异常", "file": "src/components/PostsMap.vue", "line": 247, "context": "console.error(`地理编码异常: ${address}`, error)"}, {"text": "提取地址中的关键位置信息", "file": "src/components/PostsMap.vue", "line": 252, "context": "// 提取地址中的关键位置信息"}, {"text": "香港地区关键词", "file": "src/components/PostsMap.vue", "line": 254, "context": "// 香港地区关键词"}, {"text": "沙田", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "西貢", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "尖沙咀", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "中環", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "銅鑼灣", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "旺角", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "深水埗", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "觀塘", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "荃灣", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "屯門", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "元朗", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "大埔", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "北區", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "葵青", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "離島", "file": "src/components/PostsMap.vue", "line": 255, "context": "const hkKeywords = ['沙田', '西貢', '尖沙咀', '中環', '銅鑼灣', '旺角', '深水埗', '觀塘', '荃灣', '屯門', '元朗', '大埔', '北區', '葵青', '離島']"}, {"text": "中国大陆城市关键词", "file": "src/components/PostsMap.vue", "line": 263, "context": "// 中国大陆城市关键词"}, {"text": "北京", "file": "src/components/PostsMap.vue", "line": 264, "context": "const chinaKeywords = ['北京', '上海', '广州', '深圳', '中山', '江门', '开平']"}, {"text": "上海", "file": "src/components/PostsMap.vue", "line": 264, "context": "const chinaKeywords = ['北京', '上海', '广州', '深圳', '中山', '江门', '开平']"}, {"text": "广州", "file": "src/components/PostsMap.vue", "line": 264, "context": "const chinaKeywords = ['北京', '上海', '广州', '深圳', '中山', '江门', '开平']"}, {"text": "深圳", "file": "src/components/PostsMap.vue", "line": 264, "context": "const chinaKeywords = ['北京', '上海', '广州', '深圳', '中山', '江门', '开平']"}, {"text": "中山", "file": "src/components/PostsMap.vue", "line": 264, "context": "const chinaKeywords = ['北京', '上海', '广州', '深圳', '中山', '江门', '开平']"}, {"text": "江门", "file": "src/components/PostsMap.vue", "line": 264, "context": "const chinaKeywords = ['北京', '上海', '广州', '深圳', '中山', '江门', '开平']"}, {"text": "开平", "file": "src/components/PostsMap.vue", "line": 264, "context": "const chinaKeywords = ['北京', '上海', '广州', '深圳', '中山', '江门', '开平']"}, {"text": "其他国家", "file": "src/components/PostsMap.vue", "line": 272, "context": "// 其他国家"}, {"text": "越南", "file": "src/components/PostsMap.vue", "line": 273, "context": "if (address.includes('越南')) {"}, {"text": "创建弹窗内容", "file": "src/components/PostsMap.vue", "line": 280, "context": "// 创建弹窗内容"}, {"text": "宠物", "file": "src/components/PostsMap.vue", "line": 288, "context": "${petPhoto ? `<img src=\"${petPhoto}\" alt=\"${post.pet?.name || '宠物'}\" class=\"popup-image\" />` : ''}"}, {"text": "未命名宠物", "file": "src/components/PostsMap.vue", "line": 290, "context": "<h3 class=\"popup-title\">${post.pet?.name || '未命名宠物'}</h3>"}, {"text": "走失地点", "file": "src/components/PostsMap.vue", "line": 295, "context": "<p class=\"popup-location\"><strong>走失地点：</strong>${post.last_seen_location}</p>"}, {"text": "走失时间", "file": "src/components/PostsMap.vue", "line": 296, "context": "<p class=\"popup-time\"><strong>走失时间：</strong>${lastSeenTime}</p>"}, {"text": "主人", "file": "src/components/PostsMap.vue", "line": 297, "context": "<p class=\"popup-owner\"><strong>主人：</strong>${post.owner?.username || '匿名'}</p>"}, {"text": "匿名", "file": "src/components/PostsMap.vue", "line": 297, "context": "<p class=\"popup-owner\"><strong>主人：</strong>${post.owner?.username || '匿名'}</p>"}, {"text": "描述", "file": "src/components/PostsMap.vue", "line": 298, "context": "${post.pet?.description ? `<p class=\"popup-description\"><strong>描述：</strong>${post.pet.description}</p>` : ''}"}, {"text": "查看详情", "file": "src/components/PostsMap.vue", "line": 301, "context": "<button class=\"popup-btn\" onclick=\"window.postMapSelectPost(${post.id})\">查看详情</button>"}, {"text": "加载帖子标记", "file": "src/components/PostsMap.vue", "line": 307, "context": "// 加载帖子标记"}, {"text": "开始加载帖子标记", "file": "src/components/PostsMap.vue", "line": 309, "context": "console.log('开始加载帖子标记...')"}, {"text": "地图实例", "file": "src/components/PostsMap.vue", "line": 310, "context": "console.log('地图实例:', map)"}, {"text": "聚类组", "file": "src/components/PostsMap.vue", "line": 311, "context": "console.log('聚类组:', markerClusterGroup)"}, {"text": "有效帖子数量", "file": "src/components/PostsMap.vue", "line": 312, "context": "console.log('有效帖子数量:', validPosts.value.length)"}, {"text": "有效帖子", "file": "src/components/PostsMap.vue", "line": 313, "context": "console.log('有效帖子:', validPosts.value)"}, {"text": "地图或聚类组未初始化", "file": "src/components/PostsMap.vue", "line": 316, "context": "console.error('地图或聚类组未初始化')"}, {"text": "没有有效的帖子数据", "file": "src/components/PostsMap.vue", "line": 321, "context": "console.warn('没有有效的帖子数据')"}, {"text": "处理帖子", "file": "src/components/PostsMap.vue", "line": 333, "context": "console.log(`处理帖子 ${post.id}: ${post.last_seen_location}`)"}, {"text": "地理编码结果", "file": "src/components/PostsMap.vue", "line": 337, "context": "console.log(`地理编码结果:`, coords)"}, {"text": "创建标记", "file": "src/components/PostsMap.vue", "line": 340, "context": "// 创建标记"}, {"text": "添加弹窗", "file": "src/components/PostsMap.vue", "line": 345, "context": "// 添加弹窗"}, {"text": "点击事件", "file": "src/components/PostsMap.vue", "line": 352, "context": "// 点击事件"}, {"text": "添加到聚类组", "file": "src/components/PostsMap.vue", "line": 357, "context": "// 添加到聚类组"}, {"text": "成功添加标记", "file": "src/components/PostsMap.vue", "line": 362, "context": "console.log(`成功添加标记 ${post.id}`)"}, {"text": "地理编码失败", "file": "src/components/PostsMap.vue", "line": 365, "context": "console.warn(`地理编码失败 - ${post.last_seen_location}`)"}, {"text": "处理帖子", "file": "src/components/PostsMap.vue", "line": 369, "context": "console.error(`处理帖子 ${post.id} 时出错:`, error)"}, {"text": "时出错", "file": "src/components/PostsMap.vue", "line": 369, "context": "console.error(`处理帖子 ${post.id} 时出错:`, error)"}, {"text": "标记加载完成", "file": "src/components/PostsMap.vue", "line": 373, "context": "console.log(`标记加载完成: 成功 ${successCount}, 失败 ${failureCount}`)"}, {"text": "成功", "file": "src/components/PostsMap.vue", "line": 373, "context": "console.log(`标记加载完成: 成功 ${successCount}, 失败 ${failureCount}`)"}, {"text": "失败", "file": "src/components/PostsMap.vue", "line": 373, "context": "console.log(`标记加载完成: 成功 ${successCount}, 失败 ${failureCount}`)"}, {"text": "调整地图视图以显示所有标记", "file": "src/components/PostsMap.vue", "line": 375, "context": "// 调整地图视图以显示所有标记"}, {"text": "调整地图视图以显示所有标记", "file": "src/components/PostsMap.vue", "line": 377, "context": "console.log('调整地图视图以显示所有标记')"}, {"text": "没有成功的标记", "file": "src/components/PostsMap.vue", "line": 380, "context": "console.warn('没有成功的标记，无法调整地图视图')"}, {"text": "无法调整地图视图", "file": "src/components/PostsMap.vue", "line": 380, "context": "console.warn('没有成功的标记，无法调整地图视图')"}, {"text": "全局函数", "file": "src/components/PostsMap.vue", "line": 386, "context": "// 全局函数，供弹窗按钮调用"}, {"text": "供弹窗按钮调用", "file": "src/components/PostsMap.vue", "line": 386, "context": "// 全局函数，供弹窗按钮调用"}, {"text": "监听帖子变化", "file": "src/components/PostsMap.vue", "line": 391, "context": "// 监听帖子变化"}, {"text": "生命周期", "file": "src/components/PostsMap.vue", "line": 402, "context": "// 生命周期"}, {"text": "清理全局函数", "file": "src/components/PostsMap.vue", "line": 413, "context": "// 清理全局函数"}, {"text": "地图容器样式", "file": "src/components/PostsMap.vue", "line": 419, "context": "/* 地图容器样式 */"}, {"text": "自定义标记样式", "file": "src/components/PostsMap.vue", "line": 424, "context": "/* 自定义标记样式 */"}, {"text": "弹窗样式", "file": "src/components/PostsMap.vue", "line": 466, "context": "/* 弹窗样式 */"}, {"text": "聚类样式", "file": "src/components/PostsMap.vue", "line": 557, "context": "/* 聚类样式 */"}, {"text": "响应式调整", "file": "src/components/PostsMap.vue", "line": 603, "context": "/* 响应式调整 */"}, {"text": "标题", "file": "src/components/ShareModal.vue", "line": 4, "context": "<!-- 标题 -->"}, {"text": "分享帮助寻找", "file": "src/components/ShareModal.vue", "line": 7, "context": "分享帮助寻找"}, {"text": "分享预览卡片", "file": "src/components/ShareModal.vue", "line": 19, "context": "<!-- 分享预览卡片 -->"}, {"text": "寻找走失宠物", "file": "src/components/ShareModal.vue", "line": 38, "context": "寻找走失宠物：{{ post.pet?.name }}"}, {"text": "走失地点", "file": "src/components/ShareModal.vue", "line": 45, "context": "走失地点：{{ post.last_seen_location }}"}, {"text": "走失时间", "file": "src/components/ShareModal.vue", "line": 48, "context": "走失时间：{{ formatDate(post.last_seen_time) }}"}, {"text": "分享文本", "file": "src/components/ShareModal.vue", "line": 54, "context": "<!-- 分享文本 -->"}, {"text": "分享文本", "file": "src/components/ShareModal.vue", "line": 57, "context": "分享文本（可编辑）"}, {"text": "可编辑", "file": "src/components/ShareModal.vue", "line": 57, "context": "分享文本（可编辑）"}, {"text": "分享平台", "file": "src/components/ShareModal.vue", "line": 66, "context": "<!-- 分享平台 -->"}, {"text": "选择分享平台", "file": "src/components/ShareModal.vue", "line": 68, "context": "<h4 class=\"text-sm font-medium text-gray-700 mb-3\">选择分享平台</h4>"}, {"text": "复制链接", "file": "src/components/ShareModal.vue", "line": 87, "context": "<!-- 复制链接 -->"}, {"text": "直接复制链接", "file": "src/components/ShareModal.vue", "line": 90, "context": "直接复制链接"}, {"text": "已复制", "file": "src/components/ShareModal.vue", "line": 102, "context": "{{ copied ? '已复制' : '复制' }}"}, {"text": "复制", "file": "src/components/ShareModal.vue", "line": 102, "context": "{{ copied ? '已复制' : '复制' }}"}, {"text": "原生分享", "file": "src/components/ShareModal.vue", "line": 107, "context": "<!-- 原生分享 -->"}, {"text": "使用系统分享", "file": "src/components/ShareModal.vue", "line": 113, "context": "使用系统分享"}, {"text": "关闭按钮", "file": "src/components/ShareModal.vue", "line": 117, "context": "<!-- 关闭按钮 -->"}, {"text": "关闭", "file": "src/components/ShareModal.vue", "line": 123, "context": "关闭"}, {"text": "状态", "file": "src/components/ShareModal.vue", "line": 147, "context": "// 状态"}, {"text": "计算属性", "file": "src/components/ShareModal.vue", "line": 150, "context": "// 计算属性"}, {"text": "方法", "file": "src/components/ShareModal.vue", "line": 161, "context": "// 方法"}, {"text": "紧急寻宠", "file": "src/components/ShareModal.vue", "line": 170, "context": "return `🐾 紧急寻宠！🐾"}, {"text": "宠物名字", "file": "src/components/ShareModal.vue", "line": 172, "context": "宠物名字：${pet?.name}"}, {"text": "品种", "file": "src/components/ShareModal.vue", "line": 173, "context": "品种：${pet?.species}"}, {"text": "毛色", "file": "src/components/ShareModal.vue", "line": 174, "context": "毛色：${pet?.color}"}, {"text": "性别", "file": "src/components/ShareModal.vue", "line": 175, "context": "性别：${PET_GENDERS.find(g => g.value === pet?.gender)?.label}"}, {"text": "走失地点", "file": "src/components/ShareModal.vue", "line": 176, "context": "走失地点：${props.post.last_seen_location}"}, {"text": "走失时间", "file": "src/components/ShareModal.vue", "line": 177, "context": "走失时间：${formatDate(props.post.last_seen_time)}"}, {"text": "如果您看到这只宠物", "file": "src/components/ShareModal.vue", "line": 179, "context": "如果您看到这只宠物，请联系失主！"}, {"text": "请联系失主", "file": "src/components/ShareModal.vue", "line": 179, "context": "如果您看到这只宠物，请联系失主！"}, {"text": "每一次转发都可能帮助它回家", "file": "src/components/ShareModal.vue", "line": 180, "context": "每一次转发都可能帮助它回家 ❤️"}, {"text": "详情链接", "file": "src/components/ShareModal.vue", "line": 182, "context": "详情链接：${shareUrl.value}`"}, {"text": "寻找走失宠物", "file": "src/components/ShareModal.vue", "line": 205, "context": "title: `寻找走失宠物：${props.post.pet?.name}`,"}, {"text": "分享取消或失败", "file": "src/components/ShareModal.vue", "line": 210, "context": "console.log('分享取消或失败:', error)"}, {"text": "这里返回简单的", "file": "src/components/ShareModal.vue", "line": 215, "context": "// 这里返回简单的SVG图标组件"}, {"text": "图标组件", "file": "src/components/ShareModal.vue", "line": 215, "context": "// 这里返回简单的SVG图标组件"}, {"text": "标题", "file": "src/components/SightingForm.vue", "line": 4, "context": "<!-- 标题 -->"}, {"text": "提供目击线索", "file": "src/components/SightingForm.vue", "line": 7, "context": "提供目击线索"}, {"text": "宠物信息展示", "file": "src/components/SightingForm.vue", "line": 19, "context": "<!-- 宠物信息展示 -->"}, {"text": "走失地点", "file": "src/components/SightingForm.vue", "line": 42, "context": "走失地点：{{ post.last_seen_location }}"}, {"text": "表单", "file": "src/components/SightingForm.vue", "line": 48, "context": "<!-- 表单 -->"}, {"text": "目击时间", "file": "src/components/SightingForm.vue", "line": 50, "context": "<!-- 目击时间 -->"}, {"text": "目击时间", "file": "src/components/SightingForm.vue", "line": 53, "context": "目击时间 <span class=\"text-red-500\">*</span>"}, {"text": "目击地点", "file": "src/components/SightingForm.vue", "line": 64, "context": "<!-- 目击地点 -->"}, {"text": "目击地点", "file": "src/components/SightingForm.vue", "line": 67, "context": "label=\"目击地点\""}, {"text": "请输入目击地点进行搜索", "file": "src/components/SightingForm.vue", "line": 68, "context": "placeholder=\"请输入目击地点进行搜索\""}, {"text": "目击情况描述", "file": "src/components/SightingForm.vue", "line": 72, "context": "<!-- 目击情况描述 -->"}, {"text": "目击情况描述", "file": "src/components/SightingForm.vue", "line": 75, "context": "目击情况描述（可选）"}, {"text": "可选", "file": "src/components/SightingForm.vue", "line": 75, "context": "目击情况描述（可选）"}, {"text": "请描述目击时的情况", "file": "src/components/SightingForm.vue", "line": 82, "context": "placeholder=\"请描述目击时的情况，如宠物状态、行为等\""}, {"text": "如宠物状态", "file": "src/components/SightingForm.vue", "line": 82, "context": "placeholder=\"请描述目击时的情况，如宠物状态、行为等\""}, {"text": "行为等", "file": "src/components/SightingForm.vue", "line": 82, "context": "placeholder=\"请描述目击时的情况，如宠物状态、行为等\""}, {"text": "照片上传", "file": "src/components/SightingForm.vue", "line": 86, "context": "<!-- 照片上传 -->"}, {"text": "目击照片", "file": "src/components/SightingForm.vue", "line": 89, "context": "label=\"目击照片（可选）\""}, {"text": "可选", "file": "src/components/SightingForm.vue", "line": 89, "context": "label=\"目击照片（可选）\""}, {"text": "如果您拍摄了照片", "file": "src/components/SightingForm.vue", "line": 90, "context": "help-text=\"如果您拍摄了照片，请上传以帮助确认\""}, {"text": "请上传以帮助确认", "file": "src/components/SightingForm.vue", "line": 90, "context": "help-text=\"如果您拍摄了照片，请上传以帮助确认\""}, {"text": "隐私说明", "file": "src/components/SightingForm.vue", "line": 93, "context": "<!-- 隐私说明 -->"}, {"text": "隐私保护", "file": "src/components/SightingForm.vue", "line": 103, "context": "隐私保护"}, {"text": "您的线索将匿名提交", "file": "src/components/SightingForm.vue", "line": 107, "context": "您的线索将匿名提交，只有失主能看到。我们不会收集您的个人信息。"}, {"text": "只有失主能看到", "file": "src/components/SightingForm.vue", "line": 107, "context": "您的线索将匿名提交，只有失主能看到。我们不会收集您的个人信息。"}, {"text": "我们不会收集您的个人信息", "file": "src/components/SightingForm.vue", "line": 107, "context": "您的线索将匿名提交，只有失主能看到。我们不会收集您的个人信息。"}, {"text": "错误提示", "file": "src/components/SightingForm.vue", "line": 114, "context": "<!-- 错误提示 -->"}, {"text": "操作按钮", "file": "src/components/SightingForm.vue", "line": 128, "context": "<!-- 操作按钮 -->"}, {"text": "取消", "file": "src/components/SightingForm.vue", "line": 135, "context": "取消"}, {"text": "提交中", "file": "src/components/SightingForm.vue", "line": 144, "context": "提交中..."}, {"text": "提交线索", "file": "src/components/SightingForm.vue", "line": 146, "context": "<span v-else>提交线索</span>"}, {"text": "状态", "file": "src/components/SightingForm.vue", "line": 175, "context": "// 状态"}, {"text": "表单数据", "file": "src/components/SightingForm.vue", "line": 179, "context": "// 表单数据"}, {"text": "计算属性", "file": "src/components/SightingForm.vue", "line": 190, "context": "// 计算属性"}, {"text": "方法", "file": "src/components/SightingForm.vue", "line": 195, "context": "// 方法"}, {"text": "设置地点信息", "file": "src/components/SightingForm.vue", "line": 209, "context": "// 设置地点信息"}, {"text": "提交失败", "file": "src/components/SightingForm.vue", "line": 219, "context": "error.value = response.message || '提交失败'"}, {"text": "提交线索失败", "file": "src/components/SightingForm.vue", "line": 222, "context": "console.error('提交线索失败:', err)"}, {"text": "提交失败", "file": "src/components/SightingForm.vue", "line": 223, "context": "error.value = err.message || '提交失败，请稍后重试'"}, {"text": "请稍后重试", "file": "src/components/SightingForm.vue", "line": 223, "context": "error.value = err.message || '提交失败，请稍后重试'"}, {"text": "标题", "file": "src/components/SightingsModal.vue", "line": 4, "context": "<!-- 标题 -->"}, {"text": "目击线索", "file": "src/components/SightingsModal.vue", "line": 7, "context": "目击线索 - {{ post.pet?.name }}"}, {"text": "加载状态", "file": "src/components/SightingsModal.vue", "line": 19, "context": "<!-- 加载状态 -->"}, {"text": "线索列表", "file": "src/components/SightingsModal.vue", "line": 24, "context": "<!-- 线索列表 -->"}, {"text": "目击线索", "file": "src/components/SightingsModal.vue", "line": 34, "context": "目击线索 #{{ sighting.id }}"}, {"text": "已验证", "file": "src/components/SightingsModal.vue", "line": 44, "context": "{{ sighting.is_verified ? '已验证' : '待验证' }}"}, {"text": "待验证", "file": "src/components/SightingsModal.vue", "line": 44, "context": "{{ sighting.is_verified ? '已验证' : '待验证' }}"}, {"text": "取消验证", "file": "src/components/SightingsModal.vue", "line": 57, "context": "{{ sighting.is_verified ? '取消验证' : '标记为已验证' }}"}, {"text": "标记为已验证", "file": "src/components/SightingsModal.vue", "line": 57, "context": "{{ sighting.is_verified ? '取消验证' : '标记为已验证' }}"}, {"text": "线索信息", "file": "src/components/SightingsModal.vue", "line": 63, "context": "<!-- 线索信息 -->"}, {"text": "目击时间", "file": "src/components/SightingsModal.vue", "line": 66, "context": "<dt class=\"text-sm font-medium text-gray-500\">目击时间</dt>"}, {"text": "目击地点", "file": "src/components/SightingsModal.vue", "line": 70, "context": "<dt class=\"text-sm font-medium text-gray-500\">目击地点</dt>"}, {"text": "描述", "file": "src/components/SightingsModal.vue", "line": 74, "context": "<dt class=\"text-sm font-medium text-gray-500\">描述</dt>"}, {"text": "提交时间", "file": "src/components/SightingsModal.vue", "line": 78, "context": "<dt class=\"text-sm font-medium text-gray-500\">提交时间</dt>"}, {"text": "线索照片", "file": "src/components/SightingsModal.vue", "line": 83, "context": "<!-- 线索照片 -->"}, {"text": "目击照片", "file": "src/components/SightingsModal.vue", "line": 85, "context": "<dt class=\"text-sm font-medium text-gray-500 mb-2\">目击照片</dt>"}, {"text": "目击照片", "file": "src/components/SightingsModal.vue", "line": 88, "context": "alt=\"目击照片\""}, {"text": "空状态", "file": "src/components/SightingsModal.vue", "line": 97, "context": "<!-- 空状态 -->"}, {"text": "暂无线索", "file": "src/components/SightingsModal.vue", "line": 103, "context": "<h3 class=\"mt-2 text-sm font-medium text-gray-900\">暂无线索</h3>"}, {"text": "还没有人提供目击线索", "file": "src/components/SightingsModal.vue", "line": 105, "context": "还没有人提供目击线索"}, {"text": "分页", "file": "src/components/SightingsModal.vue", "line": 109, "context": "<!-- 分页 -->"}, {"text": "关闭按钮", "file": "src/components/SightingsModal.vue", "line": 119, "context": "<!-- 关闭按钮 -->"}, {"text": "关闭", "file": "src/components/SightingsModal.vue", "line": 125, "context": "关闭"}, {"text": "图片查看模态框", "file": "src/components/SightingsModal.vue", "line": 130, "context": "<!-- 图片查看模态框 -->"}, {"text": "目击照片", "file": "src/components/SightingsModal.vue", "line": 139, "context": "alt=\"目击照片\""}, {"text": "状态", "file": "src/components/SightingsModal.vue", "line": 166, "context": "// 状态"}, {"text": "图片查看相关状态", "file": "src/components/SightingsModal.vue", "line": 176, "context": "// 图片查看相关状态"}, {"text": "方法", "file": "src/components/SightingsModal.vue", "line": 180, "context": "// 方法"}, {"text": "服务器返回的格式是", "file": "src/components/SightingsModal.vue", "line": 197, "context": "// 服务器返回的格式是 { success, message, data: [...], pagination: {...} }"}, {"text": "从响应中获取分页信息", "file": "src/components/SightingsModal.vue", "line": 199, "context": "// 从响应中获取分页信息"}, {"text": "加载线索失败", "file": "src/components/SightingsModal.vue", "line": 210, "context": "console.error('加载线索失败:', error)"}, {"text": "操作失败", "file": "src/components/SightingsModal.vue", "line": 228, "context": "alert('操作失败：' + response.message)"}, {"text": "验证线索失败", "file": "src/components/SightingsModal.vue", "line": 231, "context": "console.error('验证线索失败:', error)"}, {"text": "操作失败", "file": "src/components/SightingsModal.vue", "line": 232, "context": "alert('操作失败，请稍后重试')"}, {"text": "请稍后重试", "file": "src/components/SightingsModal.vue", "line": 232, "context": "alert('操作失败，请稍后重试')"}, {"text": "欢迎信息", "file": "src/components/dashboard/DashboardOverview.vue", "line": 3, "context": "<!-- 欢迎信息 -->"}, {"text": "欢迎回来", "file": "src/components/dashboard/DashboardOverview.vue", "line": 6, "context": "欢迎回来，{{ authStore.user?.username }}！"}, {"text": "感谢您使用走失宠物协寻平台", "file": "src/components/dashboard/DashboardOverview.vue", "line": 9, "context": "感谢您使用走失宠物协寻平台，让我们一起帮助更多宠物回家。"}, {"text": "让我们一起帮助更多宠物回家", "file": "src/components/dashboard/DashboardOverview.vue", "line": 9, "context": "感谢您使用走失宠物协寻平台，让我们一起帮助更多宠物回家。"}, {"text": "统计卡片", "file": "src/components/dashboard/DashboardOverview.vue", "line": 13, "context": "<!-- 统计卡片 -->"}, {"text": "我的帖子", "file": "src/components/dashboard/DashboardOverview.vue", "line": 27, "context": "我的帖子"}, {"text": "我的宠物", "file": "src/components/dashboard/DashboardOverview.vue", "line": 49, "context": "我的宠物"}, {"text": "收到线索", "file": "src/components/dashboard/DashboardOverview.vue", "line": 72, "context": "收到线索"}, {"text": "最近活动", "file": "src/components/dashboard/DashboardOverview.vue", "line": 83, "context": "<!-- 最近活动 -->"}, {"text": "最近活动", "file": "src/components/dashboard/DashboardOverview.vue", "line": 86, "context": "<h3 class=\"text-lg font-medium text-gray-900\">最近活动</h3>"}, {"text": "暂无帖子", "file": "src/components/dashboard/DashboardOverview.vue", "line": 138, "context": "<h3 class=\"mt-2 text-sm font-medium text-gray-900\">暂无帖子</h3>"}, {"text": "您还没有发布任何寻宠信息", "file": "src/components/dashboard/DashboardOverview.vue", "line": 140, "context": "您还没有发布任何寻宠信息"}, {"text": "发布走失信息", "file": "src/components/dashboard/DashboardOverview.vue", "line": 147, "context": "发布走失信息"}, {"text": "快速操作", "file": "src/components/dashboard/DashboardOverview.vue", "line": 154, "context": "<!-- 快速操作 -->"}, {"text": "快速操作", "file": "src/components/dashboard/DashboardOverview.vue", "line": 156, "context": "<h3 class=\"text-lg font-medium text-gray-900 mb-4\">快速操作</h3>"}, {"text": "发布走失信息", "file": "src/components/dashboard/DashboardOverview.vue", "line": 170, "context": "<p class=\"text-sm font-medium text-gray-900\">发布走失信息</p>"}, {"text": "快速发布宠物走失信息", "file": "src/components/dashboard/DashboardOverview.vue", "line": 171, "context": "<p class=\"text-sm text-gray-500\">快速发布宠物走失信息</p>"}, {"text": "浏览寻宠信息", "file": "src/components/dashboard/DashboardOverview.vue", "line": 187, "context": "<p class=\"text-sm font-medium text-gray-900\">浏览寻宠信息</p>"}, {"text": "查看其他人的寻宠信息", "file": "src/components/dashboard/DashboardOverview.vue", "line": 188, "context": "<p class=\"text-sm text-gray-500\">查看其他人的寻宠信息</p>"}, {"text": "状态", "file": "src/components/dashboard/DashboardOverview.vue", "line": 206, "context": "// 状态"}, {"text": "方法", "file": "src/components/dashboard/DashboardOverview.vue", "line": 215, "context": "// 方法"}, {"text": "加载最近的帖子", "file": "src/components/dashboard/DashboardOverview.vue", "line": 220, "context": "// 加载最近的帖子"}, {"text": "服务器返回的格式是", "file": "src/components/dashboard/DashboardOverview.vue", "line": 227, "context": "// 服务器返回的格式是 { success, message, data: [...], pagination: {...} }"}, {"text": "从响应中获取分页信息", "file": "src/components/dashboard/DashboardOverview.vue", "line": 229, "context": "// 从响应中获取分页信息"}, {"text": "加载", "file": "src/components/dashboard/DashboardOverview.vue", "line": 235, "context": "console.error('加载Dashboard数据失败:', error)"}, {"text": "数据失败", "file": "src/components/dashboard/DashboardOverview.vue", "line": 235, "context": "console.error('加载Dashboard数据失败:', error)"}, {"text": "页面标题", "file": "src/components/dashboard/MyPets.vue", "line": 3, "context": "<!-- 页面标题 -->"}, {"text": "我的宠物", "file": "src/components/dashboard/MyPets.vue", "line": 7, "context": "<h2 class=\"text-lg font-medium text-gray-900\">我的宠物</h2>"}, {"text": "管理您的宠物信息和寻宠帖子", "file": "src/components/dashboard/MyPets.vue", "line": 9, "context": "管理您的宠物信息和寻宠帖子"}, {"text": "添加宠物", "file": "src/components/dashboard/MyPets.vue", "line": 16, "context": "添加宠物"}, {"text": "宠物列表", "file": "src/components/dashboard/MyPets.vue", "line": 21, "context": "<!-- 宠物列表 -->"}, {"text": "加载失败", "file": "src/components/dashboard/MyPets.vue", "line": 32, "context": "<h3 class=\"text-sm font-medium text-red-800\">加载失败</h3>"}, {"text": "重试", "file": "src/components/dashboard/MyPets.vue", "line": 39, "context": "重试"}, {"text": "还没有宠物信息", "file": "src/components/dashboard/MyPets.vue", "line": 51, "context": "<h3 class=\"mt-2 text-sm font-medium text-gray-900\">还没有宠物信息</h3>"}, {"text": "添加您的宠物信息", "file": "src/components/dashboard/MyPets.vue", "line": 53, "context": "添加您的宠物信息，方便快速发布寻宠信息"}, {"text": "方便快速发布寻宠信息", "file": "src/components/dashboard/MyPets.vue", "line": 53, "context": "添加您的宠物信息，方便快速发布寻宠信息"}, {"text": "添加第一只宠物", "file": "src/components/dashboard/MyPets.vue", "line": 60, "context": "添加第一只宠物"}, {"text": "宠物照片", "file": "src/components/dashboard/MyPets.vue", "line": 73, "context": "<!-- 宠物照片 -->"}, {"text": "宠物信息", "file": "src/components/dashboard/MyPets.vue", "line": 88, "context": "<!-- 宠物信息 -->"}, {"text": "未命名", "file": "src/components/dashboard/MyPets.vue", "line": 90, "context": "<h3 class=\"font-medium text-gray-900 mb-1\">{{ pet.name || '未命名' }}</h3>"}, {"text": "分页", "file": "src/components/dashboard/MyPets.vue", "line": 103, "context": "<!-- 分页 -->"}, {"text": "添加宠物模态框", "file": "src/components/dashboard/MyPets.vue", "line": 115, "context": "<!-- 添加宠物模态框 -->"}, {"text": "宠物详情模态框", "file": "src/components/dashboard/MyPets.vue", "line": 123, "context": "<!-- 宠物详情模态框 -->"}, {"text": "状态", "file": "src/components/dashboard/MyPets.vue", "line": 146, "context": "// 状态"}, {"text": "模态框状态", "file": "src/components/dashboard/MyPets.vue", "line": 157, "context": "// 模态框状态"}, {"text": "获取宠物列表", "file": "src/components/dashboard/MyPets.vue", "line": 162, "context": "// 获取宠物列表"}, {"text": "获取宠物列表失败", "file": "src/components/dashboard/MyPets.vue", "line": 184, "context": "throw new Error(response.message || '获取宠物列表失败')"}, {"text": "获取宠物列表失败", "file": "src/components/dashboard/MyPets.vue", "line": 187, "context": "console.error('获取宠物列表失败:', err)"}, {"text": "获取宠物列表失败", "file": "src/components/dashboard/MyPets.vue", "line": 188, "context": "error.value = err instanceof Error ? err.message : '获取宠物列表失败'"}, {"text": "处理分页变化", "file": "src/components/dashboard/MyPets.vue", "line": 194, "context": "// 处理分页变化"}, {"text": "打开宠物详情", "file": "src/components/dashboard/MyPets.vue", "line": 200, "context": "// 打开宠物详情"}, {"text": "关闭宠物详情", "file": "src/components/dashboard/MyPets.vue", "line": 206, "context": "// 关闭宠物详情"}, {"text": "处理宠物添加成功", "file": "src/components/dashboard/MyPets.vue", "line": 212, "context": "// 处理宠物添加成功"}, {"text": "处理宠物更新成功", "file": "src/components/dashboard/MyPets.vue", "line": 218, "context": "// 处理宠物更新成功"}, {"text": "处理宠物删除成功", "file": "src/components/dashboard/MyPets.vue", "line": 227, "context": "// 处理宠物删除成功"}, {"text": "获取性别标签", "file": "src/components/dashboard/MyPets.vue", "line": 233, "context": "// 获取性别标签"}, {"text": "未知", "file": "src/components/dashboard/MyPets.vue", "line": 236, "context": "return genderOption?.label || '未知'"}, {"text": "组件挂载时获取数据", "file": "src/components/dashboard/MyPets.vue", "line": 239, "context": "// 组件挂载时获取数据"}, {"text": "页面标题", "file": "src/components/dashboard/MyPosts.vue", "line": 3, "context": "<!-- 页面标题 -->"}, {"text": "我的帖子", "file": "src/components/dashboard/MyPosts.vue", "line": 7, "context": "<h2 class=\"text-lg font-medium text-gray-900\">我的帖子</h2>"}, {"text": "管理您发布的寻宠信息", "file": "src/components/dashboard/MyPosts.vue", "line": 9, "context": "管理您发布的寻宠信息"}, {"text": "发布新帖子", "file": "src/components/dashboard/MyPosts.vue", "line": 16, "context": "发布新帖子"}, {"text": "帖子列表", "file": "src/components/dashboard/MyPosts.vue", "line": 21, "context": "<!-- 帖子列表 -->"}, {"text": "宠物照片", "file": "src/components/dashboard/MyPosts.vue", "line": 34, "context": "<!-- 宠物照片 -->"}, {"text": "帖子信息", "file": "src/components/dashboard/MyPosts.vue", "line": 50, "context": "<!-- 帖子信息 -->"}, {"text": "走失地点", "file": "src/components/dashboard/MyPosts.vue", "line": 77, "context": "走失地点：{{ post.last_seen_location }}"}, {"text": "操作按钮", "file": "src/components/dashboard/MyPosts.vue", "line": 80, "context": "<!-- 操作按钮 -->"}, {"text": "查看详情", "file": "src/components/dashboard/MyPosts.vue", "line": 86, "context": "查看详情"}, {"text": "查看线索", "file": "src/components/dashboard/MyPosts.vue", "line": 93, "context": "查看线索"}, {"text": "寻找中", "file": "src/components/dashboard/MyPosts.vue", "line": 102, "context": "<option value=\"searching\">寻找中</option>"}, {"text": "已找到", "file": "src/components/dashboard/MyPosts.vue", "line": 103, "context": "<option value=\"found\">已找到</option>"}, {"text": "已关闭", "file": "src/components/dashboard/MyPosts.vue", "line": 104, "context": "<option value=\"closed\">已关闭</option>"}, {"text": "删除", "file": "src/components/dashboard/MyPosts.vue", "line": 112, "context": "删除"}, {"text": "暂无帖子", "file": "src/components/dashboard/MyPosts.vue", "line": 124, "context": "<h3 class=\"mt-2 text-sm font-medium text-gray-900\">暂无帖子</h3>"}, {"text": "您还没有发布任何寻宠信息", "file": "src/components/dashboard/MyPosts.vue", "line": 126, "context": "您还没有发布任何寻宠信息"}, {"text": "发布走失信息", "file": "src/components/dashboard/MyPosts.vue", "line": 133, "context": "发布走失信息"}, {"text": "分页", "file": "src/components/dashboard/MyPosts.vue", "line": 139, "context": "<!-- 分页 -->"}, {"text": "线索查看模态框", "file": "src/components/dashboard/MyPosts.vue", "line": 149, "context": "<!-- 线索查看模态框 -->"}, {"text": "状态", "file": "src/components/dashboard/MyPosts.vue", "line": 167, "context": "// 状态"}, {"text": "线索查看相关状态", "file": "src/components/dashboard/MyPosts.vue", "line": 177, "context": "// 线索查看相关状态"}, {"text": "方法", "file": "src/components/dashboard/MyPosts.vue", "line": 181, "context": "// 方法"}, {"text": "服务器返回的格式是", "file": "src/components/dashboard/MyPosts.vue", "line": 192, "context": "// 服务器返回的格式是 { success, message, data: [...], pagination: {...} }"}, {"text": "从响应中获取分页信息", "file": "src/components/dashboard/MyPosts.vue", "line": 194, "context": "// 从响应中获取分页信息"}, {"text": "加载帖子失败", "file": "src/components/dashboard/MyPosts.vue", "line": 205, "context": "console.error('加载帖子失败:', error)"}, {"text": "更新本地状态", "file": "src/components/dashboard/MyPosts.vue", "line": 221, "context": "// 更新本地状态"}, {"text": "更新状态失败", "file": "src/components/dashboard/MyPosts.vue", "line": 227, "context": "alert('更新状态失败：' + response.message)"}, {"text": "更新帖子状态失败", "file": "src/components/dashboard/MyPosts.vue", "line": 230, "context": "console.error('更新帖子状态失败:', error)"}, {"text": "更新状态失败", "file": "src/components/dashboard/MyPosts.vue", "line": 231, "context": "alert('更新状态失败，请稍后重试')"}, {"text": "请稍后重试", "file": "src/components/dashboard/MyPosts.vue", "line": 231, "context": "alert('更新状态失败，请稍后重试')"}, {"text": "确定要删除帖子", "file": "src/components/dashboard/MyPosts.vue", "line": 236, "context": "if (!confirm(`确定要删除帖子\"${post.pet?.name}\"吗？此操作不可恢复。`)) {"}, {"text": "此操作不可恢复", "file": "src/components/dashboard/MyPosts.vue", "line": 236, "context": "if (!confirm(`确定要删除帖子\"${post.pet?.name}\"吗？此操作不可恢复。`)) {"}, {"text": "从列表中移除", "file": "src/components/dashboard/MyPosts.vue", "line": 244, "context": "// 从列表中移除"}, {"text": "删除失败", "file": "src/components/dashboard/MyPosts.vue", "line": 248, "context": "alert('删除失败：' + response.message)"}, {"text": "删除帖子失败", "file": "src/components/dashboard/MyPosts.vue", "line": 251, "context": "console.error('删除帖子失败:', error)"}, {"text": "删除失败", "file": "src/components/dashboard/MyPosts.vue", "line": 252, "context": "alert('删除失败，请稍后重试')"}, {"text": "请稍后重试", "file": "src/components/dashboard/MyPosts.vue", "line": 252, "context": "alert('删除失败，请稍后重试')"}, {"text": "基本信息", "file": "src/components/dashboard/UserProfile.vue", "line": 3, "context": "<!-- 基本信息 -->"}, {"text": "基本信息", "file": "src/components/dashboard/UserProfile.vue", "line": 5, "context": "<h2 class=\"text-lg font-medium text-gray-900 mb-4\">基本信息</h2>"}, {"text": "用户名", "file": "src/components/dashboard/UserProfile.vue", "line": 9, "context": "<label class=\"block text-sm font-medium text-gray-700\">用户名</label>"}, {"text": "邮箱", "file": "src/components/dashboard/UserProfile.vue", "line": 13, "context": "<label class=\"block text-sm font-medium text-gray-700\">邮箱</label>"}, {"text": "手机号", "file": "src/components/dashboard/UserProfile.vue", "line": 17, "context": "<label class=\"block text-sm font-medium text-gray-700\">手机号</label>"}, {"text": "未设置", "file": "src/components/dashboard/UserProfile.vue", "line": 19, "context": "{{ authStore.user?.phone_number || '未设置' }}"}, {"text": "注册时间", "file": "src/components/dashboard/UserProfile.vue", "line": 23, "context": "<label class=\"block text-sm font-medium text-gray-700\">注册时间</label>"}, {"text": "修改密码", "file": "src/components/dashboard/UserProfile.vue", "line": 31, "context": "<!-- 修改密码 -->"}, {"text": "修改密码", "file": "src/components/dashboard/UserProfile.vue", "line": 33, "context": "<h2 class=\"text-lg font-medium text-gray-900 mb-4\">修改密码</h2>"}, {"text": "当前密码", "file": "src/components/dashboard/UserProfile.vue", "line": 38, "context": "当前密码"}, {"text": "请输入当前密码", "file": "src/components/dashboard/UserProfile.vue", "line": 46, "context": "placeholder=\"请输入当前密码\""}, {"text": "新密码", "file": "src/components/dashboard/UserProfile.vue", "line": 52, "context": "新密码"}, {"text": "请输入新密码", "file": "src/components/dashboard/UserProfile.vue", "line": 61, "context": "placeholder=\"请输入新密码（至少6位）\""}, {"text": "至少", "file": "src/components/dashboard/UserProfile.vue", "line": 61, "context": "placeholder=\"请输入新密码（至少6位）\""}, {"text": "确认新密码", "file": "src/components/dashboard/UserProfile.vue", "line": 67, "context": "确认新密码"}, {"text": "请再次输入新密码", "file": "src/components/dashboard/UserProfile.vue", "line": 75, "context": "placeholder=\"请再次输入新密码\""}, {"text": "错误提示", "file": "src/components/dashboard/UserProfile.vue", "line": 79, "context": "<!-- 错误提示 -->"}, {"text": "成功提示", "file": "src/components/dashboard/UserProfile.vue", "line": 93, "context": "<!-- 成功提示 -->"}, {"text": "密码修改成功", "file": "src/components/dashboard/UserProfile.vue", "line": 103, "context": "密码修改成功！"}, {"text": "修改中", "file": "src/components/dashboard/UserProfile.vue", "line": 117, "context": "修改中..."}, {"text": "修改密码", "file": "src/components/dashboard/UserProfile.vue", "line": 119, "context": "<span v-else>修改密码</span>"}, {"text": "账户统计", "file": "src/components/dashboard/UserProfile.vue", "line": 125, "context": "<!-- 账户统计 -->"}, {"text": "账户统计", "file": "src/components/dashboard/UserProfile.vue", "line": 127, "context": "<h2 class=\"text-lg font-medium text-gray-900 mb-4\">账户统计</h2>"}, {"text": "发布的帖子", "file": "src/components/dashboard/UserProfile.vue", "line": 132, "context": "<div class=\"text-sm text-gray-500\">发布的帖子</div>"}, {"text": "收到的线索", "file": "src/components/dashboard/UserProfile.vue", "line": 136, "context": "<div class=\"text-sm text-gray-500\">收到的线索</div>"}, {"text": "找回的宠物", "file": "src/components/dashboard/UserProfile.vue", "line": 140, "context": "<div class=\"text-sm text-gray-500\">找回的宠物</div>"}, {"text": "状态", "file": "src/components/dashboard/UserProfile.vue", "line": 155, "context": "// 状态"}, {"text": "表单数据", "file": "src/components/dashboard/UserProfile.vue", "line": 160, "context": "// 表单数据"}, {"text": "计算属性", "file": "src/components/dashboard/UserProfile.vue", "line": 167, "context": "// 计算属性"}, {"text": "方法", "file": "src/components/dashboard/UserProfile.vue", "line": 173, "context": "// 方法"}, {"text": "验证表单", "file": "src/components/dashboard/UserProfile.vue", "line": 178, "context": "// 验证表单"}, {"text": "请输入当前密码", "file": "src/components/dashboard/UserProfile.vue", "line": 180, "context": "error.value = '请输入当前密码'"}, {"text": "新密码至少需要", "file": "src/components/dashboard/UserProfile.vue", "line": 185, "context": "error.value = '新密码至少需要6个字符'"}, {"text": "个字符", "file": "src/components/dashboard/UserProfile.vue", "line": 185, "context": "error.value = '新密码至少需要6个字符'"}, {"text": "两次输入的密码不一致", "file": "src/components/dashboard/UserProfile.vue", "line": 190, "context": "error.value = '两次输入的密码不一致'"}, {"text": "清空表单", "file": "src/components/dashboard/UserProfile.vue", "line": 204, "context": "// 清空表单"}, {"text": "秒后隐藏成功提示", "file": "src/components/dashboard/UserProfile.vue", "line": 211, "context": "// 3秒后隐藏成功提示"}, {"text": "修改密码失败", "file": "src/components/dashboard/UserProfile.vue", "line": 216, "context": "error.value = authStore.error || '修改密码失败'"}, {"text": "修改密码失败", "file": "src/components/dashboard/UserProfile.vue", "line": 219, "context": "error.value = err.message || '修改密码失败，请稍后重试'"}, {"text": "请稍后重试", "file": "src/components/dashboard/UserProfile.vue", "line": 219, "context": "error.value = err.message || '修改密码失败，请稍后重试'"}, {"text": "宠物品种选项", "file": "src/constants/index.ts", "line": 1, "context": "// 宠物品种选项"}, {"text": "兔子", "file": "src/constants/index.ts", "line": 5, "context": "{ value: '兔子', label: '兔子' },"}, {"text": "兔子", "file": "src/constants/index.ts", "line": 5, "context": "{ value: '兔子', label: '兔子' },"}, {"text": "鸟类", "file": "src/constants/index.ts", "line": 6, "context": "{ value: '鸟类', label: '鸟类' },"}, {"text": "鸟类", "file": "src/constants/index.ts", "line": 6, "context": "{ value: '鸟类', label: '鸟类' },"}, {"text": "仓鼠", "file": "src/constants/index.ts", "line": 7, "context": "{ value: '仓鼠', label: '仓鼠' },"}, {"text": "仓鼠", "file": "src/constants/index.ts", "line": 7, "context": "{ value: '仓鼠', label: '仓鼠' },"}, {"text": "其他", "file": "src/constants/index.ts", "line": 8, "context": "{ value: '其他', label: '其他' },"}, {"text": "其他", "file": "src/constants/index.ts", "line": 8, "context": "{ value: '其他', label: '其他' },"}, {"text": "宠物性别选项", "file": "src/constants/index.ts", "line": 11, "context": "// 宠物性别选项"}, {"text": "雄性", "file": "src/constants/index.ts", "line": 13, "context": "{ value: 'male', label: '雄性' },"}, {"text": "雌性", "file": "src/constants/index.ts", "line": 14, "context": "{ value: 'female', label: '雌性' },"}, {"text": "不确定", "file": "src/constants/index.ts", "line": 15, "context": "{ value: 'unknown', label: '不确定' },"}, {"text": "宠物毛色选项", "file": "src/constants/index.ts", "line": 18, "context": "// 宠物毛色选项"}, {"text": "黑色", "file": "src/constants/index.ts", "line": 20, "context": "{ value: '黑色', label: '黑色' },"}, {"text": "黑色", "file": "src/constants/index.ts", "line": 20, "context": "{ value: '黑色', label: '黑色' },"}, {"text": "白色", "file": "src/constants/index.ts", "line": 21, "context": "{ value: '白色', label: '白色' },"}, {"text": "白色", "file": "src/constants/index.ts", "line": 21, "context": "{ value: '白色', label: '白色' },"}, {"text": "棕色", "file": "src/constants/index.ts", "line": 22, "context": "{ value: '棕色', label: '棕色' },"}, {"text": "棕色", "file": "src/constants/index.ts", "line": 22, "context": "{ value: '棕色', label: '棕色' },"}, {"text": "金色", "file": "src/constants/index.ts", "line": 23, "context": "{ value: '金色', label: '金色' },"}, {"text": "金色", "file": "src/constants/index.ts", "line": 23, "context": "{ value: '金色', label: '金色' },"}, {"text": "灰色", "file": "src/constants/index.ts", "line": 24, "context": "{ value: '灰色', label: '灰色' },"}, {"text": "灰色", "file": "src/constants/index.ts", "line": 24, "context": "{ value: '灰色', label: '灰色' },"}, {"text": "花色", "file": "src/constants/index.ts", "line": 25, "context": "{ value: '花色', label: '花色' },"}, {"text": "花色", "file": "src/constants/index.ts", "line": 25, "context": "{ value: '花色', label: '花色' },"}, {"text": "其他", "file": "src/constants/index.ts", "line": 26, "context": "{ value: '其他', label: '其他' },"}, {"text": "其他", "file": "src/constants/index.ts", "line": 26, "context": "{ value: '其他', label: '其他' },"}, {"text": "香港地区选项", "file": "src/constants/index.ts", "line": 29, "context": "// 香港地区选项"}, {"text": "中西区", "file": "src/constants/index.ts", "line": 31, "context": "{ value: '中西区', label: '中西区' },"}, {"text": "中西区", "file": "src/constants/index.ts", "line": 31, "context": "{ value: '中西区', label: '中西区' },"}, {"text": "湾仔区", "file": "src/constants/index.ts", "line": 32, "context": "{ value: '湾仔区', label: '湾仔区' },"}, {"text": "湾仔区", "file": "src/constants/index.ts", "line": 32, "context": "{ value: '湾仔区', label: '湾仔区' },"}, {"text": "东区", "file": "src/constants/index.ts", "line": 33, "context": "{ value: '东区', label: '东区' },"}, {"text": "东区", "file": "src/constants/index.ts", "line": 33, "context": "{ value: '东区', label: '东区' },"}, {"text": "南区", "file": "src/constants/index.ts", "line": 34, "context": "{ value: '南区', label: '南区' },"}, {"text": "南区", "file": "src/constants/index.ts", "line": 34, "context": "{ value: '南区', label: '南区' },"}, {"text": "油尖旺区", "file": "src/constants/index.ts", "line": 35, "context": "{ value: '油尖旺区', label: '油尖旺区' },"}, {"text": "油尖旺区", "file": "src/constants/index.ts", "line": 35, "context": "{ value: '油尖旺区', label: '油尖旺区' },"}, {"text": "深水埗区", "file": "src/constants/index.ts", "line": 36, "context": "{ value: '深水埗区', label: '深水埗区' },"}, {"text": "深水埗区", "file": "src/constants/index.ts", "line": 36, "context": "{ value: '深水埗区', label: '深水埗区' },"}, {"text": "九龙城区", "file": "src/constants/index.ts", "line": 37, "context": "{ value: '九龙城区', label: '九龙城区' },"}, {"text": "九龙城区", "file": "src/constants/index.ts", "line": 37, "context": "{ value: '九龙城区', label: '九龙城区' },"}, {"text": "黄大仙区", "file": "src/constants/index.ts", "line": 38, "context": "{ value: '黄大仙区', label: '黄大仙区' },"}, {"text": "黄大仙区", "file": "src/constants/index.ts", "line": 38, "context": "{ value: '黄大仙区', label: '黄大仙区' },"}, {"text": "观塘区", "file": "src/constants/index.ts", "line": 39, "context": "{ value: '观塘区', label: '观塘区' },"}, {"text": "观塘区", "file": "src/constants/index.ts", "line": 39, "context": "{ value: '观塘区', label: '观塘区' },"}, {"text": "荃湾区", "file": "src/constants/index.ts", "line": 40, "context": "{ value: '荃湾区', label: '荃湾区' },"}, {"text": "荃湾区", "file": "src/constants/index.ts", "line": 40, "context": "{ value: '荃湾区', label: '荃湾区' },"}, {"text": "屯门区", "file": "src/constants/index.ts", "line": 41, "context": "{ value: '屯门区', label: '屯门区' },"}, {"text": "屯门区", "file": "src/constants/index.ts", "line": 41, "context": "{ value: '屯门区', label: '屯门区' },"}, {"text": "元朗区", "file": "src/constants/index.ts", "line": 42, "context": "{ value: '元朗区', label: '元朗区' },"}, {"text": "元朗区", "file": "src/constants/index.ts", "line": 42, "context": "{ value: '元朗区', label: '元朗区' },"}, {"text": "北区", "file": "src/constants/index.ts", "line": 43, "context": "{ value: '北区', label: '北区' },"}, {"text": "北区", "file": "src/constants/index.ts", "line": 43, "context": "{ value: '北区', label: '北区' },"}, {"text": "大埔区", "file": "src/constants/index.ts", "line": 44, "context": "{ value: '大埔区', label: '大埔区' },"}, {"text": "大埔区", "file": "src/constants/index.ts", "line": 44, "context": "{ value: '大埔区', label: '大埔区' },"}, {"text": "沙田区", "file": "src/constants/index.ts", "line": 45, "context": "{ value: '沙田区', label: '沙田区' },"}, {"text": "沙田区", "file": "src/constants/index.ts", "line": 45, "context": "{ value: '沙田区', label: '沙田区' },"}, {"text": "西贡区", "file": "src/constants/index.ts", "line": 46, "context": "{ value: '西贡区', label: '西贡区' },"}, {"text": "西贡区", "file": "src/constants/index.ts", "line": 46, "context": "{ value: '西贡区', label: '西贡区' },"}, {"text": "葵青区", "file": "src/constants/index.ts", "line": 47, "context": "{ value: '葵青区', label: '葵青区' },"}, {"text": "葵青区", "file": "src/constants/index.ts", "line": 47, "context": "{ value: '葵青区', label: '葵青区' },"}, {"text": "离岛区", "file": "src/constants/index.ts", "line": 48, "context": "{ value: '离岛区', label: '离岛区' },"}, {"text": "离岛区", "file": "src/constants/index.ts", "line": 48, "context": "{ value: '离岛区', label: '离岛区' },"}, {"text": "帖子状态", "file": "src/constants/index.ts", "line": 51, "context": "// 帖子状态"}, {"text": "寻找中", "file": "src/constants/index.ts", "line": 59, "context": "[POST_STATUS.SEARCHING]: '寻找中',"}, {"text": "已找到", "file": "src/constants/index.ts", "line": 60, "context": "[POST_STATUS.FOUND]: '已找到',"}, {"text": "已关闭", "file": "src/constants/index.ts", "line": 61, "context": "[POST_STATUS.CLOSED]: '已关闭',"}, {"text": "审核状态", "file": "src/constants/index.ts", "line": 64, "context": "// 审核状态"}, {"text": "待审核", "file": "src/constants/index.ts", "line": 72, "context": "[ADMIN_STATUS.PENDING]: '待审核',"}, {"text": "已通过", "file": "src/constants/index.ts", "line": 73, "context": "[ADMIN_STATUS.APPROVED]: '已通过',"}, {"text": "已拒绝", "file": "src/constants/index.ts", "line": 74, "context": "[ADMIN_STATUS.REJECTED]: '已拒绝',"}, {"text": "分页配置", "file": "src/constants/index.ts", "line": 77, "context": "// 分页配置"}, {"text": "文件上传配置", "file": "src/constants/index.ts", "line": 84, "context": "// 文件上传配置"}, {"text": "地图配置", "file": "src/constants/index.ts", "line": 91, "context": "// 地图配置"}, {"text": "香港中心坐标", "file": "src/constants/index.ts", "line": 93, "context": "DEFAULT_CENTER: [22.3193, 114.1694], // 香港中心坐标"}, {"text": "社交分享平台", "file": "src/constants/index.ts", "line": 99, "context": "// 社交分享平台"}, {"text": "错误消息", "file": "src/constants/index.ts", "line": 127, "context": "// 错误消息"}, {"text": "网络连接失败", "file": "src/constants/index.ts", "line": 129, "context": "NETWORK_ERROR: '网络连接失败，请检查网络设置',"}, {"text": "请检查网络设置", "file": "src/constants/index.ts", "line": 129, "context": "NETWORK_ERROR: '网络连接失败，请检查网络设置',"}, {"text": "服务器错误", "file": "src/constants/index.ts", "line": 130, "context": "SERVER_ERROR: '服务器错误，请稍后重试',"}, {"text": "请稍后重试", "file": "src/constants/index.ts", "line": 130, "context": "SERVER_ERROR: '服务器错误，请稍后重试',"}, {"text": "登录已过期", "file": "src/constants/index.ts", "line": 131, "context": "UNAUTHORIZED: '登录已过期，请重新登录',"}, {"text": "请重新登录", "file": "src/constants/index.ts", "line": 131, "context": "UNAUTHORIZED: '登录已过期，请重新登录',"}, {"text": "没有权限执行此操作", "file": "src/constants/index.ts", "line": 132, "context": "FORBIDDEN: '没有权限执行此操作',"}, {"text": "请求的资源不存在", "file": "src/constants/index.ts", "line": 133, "context": "NOT_FOUND: '请求的资源不存在',"}, {"text": "输入信息有误", "file": "src/constants/index.ts", "line": 134, "context": "VALIDATION_ERROR: '输入信息有误，请检查后重试',"}, {"text": "请检查后重试", "file": "src/constants/index.ts", "line": 134, "context": "VALIDATION_ERROR: '输入信息有误，请检查后重试',"}, {"text": "文件大小超过限制", "file": "src/constants/index.ts", "line": 135, "context": "FILE_TOO_LARGE: '文件大小超过限制',"}, {"text": "不支持的文件类型", "file": "src/constants/index.ts", "line": 136, "context": "INVALID_FILE_TYPE: '不支持的文件类型',"}, {"text": "成功消息", "file": "src/constants/index.ts", "line": 139, "context": "// 成功消息"}, {"text": "登录成功", "file": "src/constants/index.ts", "line": 141, "context": "LOGIN_SUCCESS: '登录成功',"}, {"text": "注册成功", "file": "src/constants/index.ts", "line": 142, "context": "REGISTER_SUCCESS: '注册成功',"}, {"text": "登出成功", "file": "src/constants/index.ts", "line": 143, "context": "LOGOUT_SUCCESS: '登出成功',"}, {"text": "创建成功", "file": "src/constants/index.ts", "line": 144, "context": "CREATE_SUCCESS: '创建成功',"}, {"text": "更新成功", "file": "src/constants/index.ts", "line": 145, "context": "UPDATE_SUCCESS: '更新成功',"}, {"text": "删除成功", "file": "src/constants/index.ts", "line": 146, "context": "DELETE_SUCCESS: '删除成功',"}, {"text": "上传成功", "file": "src/constants/index.ts", "line": 147, "context": "UPLOAD_SUCCESS: '上传成功',"}, {"text": "复制成功", "file": "src/constants/index.ts", "line": 148, "context": "COPY_SUCCESS: '复制成功',"}, {"text": "支持的语言列表", "file": "src/locales/index.ts", "line": 5, "context": "// 支持的语言列表"}, {"text": "简体中文", "file": "src/locales/index.ts", "line": 7, "context": "{ code: 'zh-C<PERSON>', name: '简体中文', flag: '🇨🇳' },"}, {"text": "繁體中文", "file": "src/locales/index.ts", "line": 8, "context": "{ code: 'zh-HK', name: '繁體中文（香港）', flag: '🇭🇰' }"}, {"text": "香港", "file": "src/locales/index.ts", "line": 8, "context": "{ code: 'zh-HK', name: '繁體中文（香港）', flag: '🇭🇰' }"}, {"text": "获取浏览器默认语言", "file": "src/locales/index.ts", "line": 13, "context": "// 获取浏览器默认语言"}, {"text": "检查浏览器语言", "file": "src/locales/index.ts", "line": 20, "context": "// 检查浏览器语言"}, {"text": "默认简体中文", "file": "src/locales/index.ts", "line": 26, "context": "return 'zh-CN' // 默认简体中文"}, {"text": "创建", "file": "src/locales/index.ts", "line": 29, "context": "// 创建 i18n 实例"}, {"text": "实例", "file": "src/locales/index.ts", "line": 29, "context": "// 创建 i18n 实例"}, {"text": "使用", "file": "src/locales/index.ts", "line": 31, "context": "legacy: false, // 使用 Composition API"}, {"text": "全局注入", "file": "src/locales/index.ts", "line": 38, "context": "globalInjection: true // 全局注入 $t"}, {"text": "切换语言的工具函数", "file": "src/locales/index.ts", "line": 41, "context": "// 切换语言的工具函数"}, {"text": "获取当前语言", "file": "src/locales/index.ts", "line": 48, "context": "// 获取当前语言"}, {"text": "获取语言显示名称", "file": "src/locales/index.ts", "line": 53, "context": "// 获取语言显示名称"}, {"text": "全局路由守卫", "file": "src/router/index.ts", "line": 50, "context": "// 全局路由守卫"}, {"text": "初始化认证状态", "file": "src/router/index.ts", "line": 52, "context": "// 初始化认证状态"}, {"text": "应用认证守卫", "file": "src/router/index.ts", "line": 56, "context": "// 应用认证守卫"}, {"text": "认证相关", "file": "src/services/auth.ts", "line": 11, "context": "* 认证相关API服务"}, {"text": "服务", "file": "src/services/auth.ts", "line": 11, "context": "* 认证相关API服务"}, {"text": "用户注册", "file": "src/services/auth.ts", "line": 15, "context": "* 用户注册"}, {"text": "用户登录", "file": "src/services/auth.ts", "line": 22, "context": "* 用户登录"}, {"text": "获取当前用户信息", "file": "src/services/auth.ts", "line": 29, "context": "* 获取当前用户信息"}, {"text": "修改密码", "file": "src/services/auth.ts", "line": 36, "context": "* 修改密码"}, {"text": "刷新", "file": "src/services/auth.ts", "line": 43, "context": "* 刷新token"}, {"text": "登出", "file": "src/services/auth.ts", "line": 50, "context": "* 登出"}, {"text": "宠物相关", "file": "src/services/pets.ts", "line": 11, "context": "* 宠物相关API服务"}, {"text": "服务", "file": "src/services/pets.ts", "line": 11, "context": "* 宠物相关API服务"}, {"text": "创建宠物信息", "file": "src/services/pets.ts", "line": 15, "context": "* 创建宠物信息"}, {"text": "添加宠物基本信息", "file": "src/services/pets.ts", "line": 20, "context": "// 添加宠物基本信息"}, {"text": "获取我的宠物列表", "file": "src/services/pets.ts", "line": 35, "context": "* 获取我的宠物列表"}, {"text": "根据", "file": "src/services/pets.ts", "line": 47, "context": "* 根据ID获取宠物详情"}, {"text": "获取宠物详情", "file": "src/services/pets.ts", "line": 47, "context": "* 根据ID获取宠物详情"}, {"text": "更新宠物信息", "file": "src/services/pets.ts", "line": 54, "context": "* 更新宠物信息"}, {"text": "添加宠物基本信息", "file": "src/services/pets.ts", "line": 59, "context": "// 添加宠物基本信息"}, {"text": "删除宠物", "file": "src/services/pets.ts", "line": 74, "context": "* 删除宠物"}, {"text": "搜索宠物", "file": "src/services/pets.ts", "line": 81, "context": "* 搜索宠物"}, {"text": "上传宠物照片", "file": "src/services/pets.ts", "line": 99, "context": "* 上传宠物照片"}, {"text": "帖子相关", "file": "src/services/posts.ts", "line": 11, "context": "* 帖子相关API服务"}, {"text": "服务", "file": "src/services/posts.ts", "line": 11, "context": "* 帖子相关API服务"}, {"text": "创建寻宠帖子", "file": "src/services/posts.ts", "line": 15, "context": "* 创建寻宠帖子"}, {"text": "获取帖子列表", "file": "src/services/posts.ts", "line": 22, "context": "* 获取帖子列表（公开）"}, {"text": "公开", "file": "src/services/posts.ts", "line": 22, "context": "* 获取帖子列表（公开）"}, {"text": "获取我的帖子列表", "file": "src/services/posts.ts", "line": 42, "context": "* 获取我的帖子列表"}, {"text": "根据", "file": "src/services/posts.ts", "line": 54, "context": "* 根据ID获取帖子详情"}, {"text": "获取帖子详情", "file": "src/services/posts.ts", "line": 54, "context": "* 根据ID获取帖子详情"}, {"text": "更新帖子信息", "file": "src/services/posts.ts", "line": 61, "context": "* 更新帖子信息"}, {"text": "更新帖子状态", "file": "src/services/posts.ts", "line": 68, "context": "* 更新帖子状态"}, {"text": "删除帖子", "file": "src/services/posts.ts", "line": 75, "context": "* 删除帖子"}, {"text": "线索相关", "file": "src/services/sightings.ts", "line": 10, "context": "* 线索相关API服务"}, {"text": "服务", "file": "src/services/sightings.ts", "line": 10, "context": "* 线索相关API服务"}, {"text": "提交目击线索", "file": "src/services/sightings.ts", "line": 14, "context": "* 提交目击线索（匿名）"}, {"text": "匿名", "file": "src/services/sightings.ts", "line": 14, "context": "* 提交目击线索（匿名）"}, {"text": "添加线索基本信息", "file": "src/services/sightings.ts", "line": 19, "context": "// 添加线索基本信息"}, {"text": "获取帖子的线索列表", "file": "src/services/sightings.ts", "line": 31, "context": "* 获取帖子的线索列表（仅帖子作者）"}, {"text": "仅帖子作者", "file": "src/services/sightings.ts", "line": 31, "context": "* 获取帖子的线索列表（仅帖子作者）"}, {"text": "获取线索详情", "file": "src/services/sightings.ts", "line": 43, "context": "* 获取线索详情（仅帖子作者）"}, {"text": "仅帖子作者", "file": "src/services/sightings.ts", "line": 43, "context": "* 获取线索详情（仅帖子作者）"}, {"text": "验证线索", "file": "src/services/sightings.ts", "line": 50, "context": "* 验证线索"}, {"text": "上传线索照片", "file": "src/services/sightings.ts", "line": 57, "context": "* 上传线索照片"}, {"text": "状态", "file": "src/stores/auth.ts", "line": 7, "context": "// 状态"}, {"text": "计算属性", "file": "src/stores/auth.ts", "line": 13, "context": "// 计算属性"}, {"text": "初始化认证状态", "file": "src/stores/auth.ts", "line": 17, "context": "// 初始化认证状态"}, {"text": "如果解析失败", "file": "src/stores/auth.ts", "line": 27, "context": "// 如果解析失败，清除本地存储"}, {"text": "清除本地存储", "file": "src/stores/auth.ts", "line": 27, "context": "// 如果解析失败，清除本地存储"}, {"text": "清除认证状态", "file": "src/stores/auth.ts", "line": 33, "context": "// 清除认证状态"}, {"text": "设置认证状态", "file": "src/stores/auth.ts", "line": 42, "context": "// 设置认证状态"}, {"text": "用户注册", "file": "src/stores/auth.ts", "line": 50, "context": "// 用户注册"}, {"text": "注册失败", "file": "src/stores/auth.ts", "line": 62, "context": "error.value = response.message || '注册失败'"}, {"text": "注册失败", "file": "src/stores/auth.ts", "line": 66, "context": "error.value = err.message || '注册失败，请稍后重试'"}, {"text": "请稍后重试", "file": "src/stores/auth.ts", "line": 66, "context": "error.value = err.message || '注册失败，请稍后重试'"}, {"text": "用户登录", "file": "src/stores/auth.ts", "line": 73, "context": "// 用户登录"}, {"text": "登录失败", "file": "src/stores/auth.ts", "line": 85, "context": "error.value = response.message || '登录失败'"}, {"text": "登录失败", "file": "src/stores/auth.ts", "line": 89, "context": "error.value = err.message || '登录失败，请稍后重试'"}, {"text": "请稍后重试", "file": "src/stores/auth.ts", "line": 89, "context": "error.value = err.message || '登录失败，请稍后重试'"}, {"text": "获取当前用户信息", "file": "src/stores/auth.ts", "line": 96, "context": "// 获取当前用户信息"}, {"text": "修改密码", "file": "src/stores/auth.ts", "line": 120, "context": "// 修改密码"}, {"text": "修改密码失败", "file": "src/stores/auth.ts", "line": 134, "context": "error.value = response.message || '修改密码失败'"}, {"text": "修改密码失败", "file": "src/stores/auth.ts", "line": 138, "context": "error.value = err.message || '修改密码失败，请稍后重试'"}, {"text": "请稍后重试", "file": "src/stores/auth.ts", "line": 138, "context": "error.value = err.message || '修改密码失败，请稍后重试'"}, {"text": "登出", "file": "src/stores/auth.ts", "line": 145, "context": "// 登出"}, {"text": "即使服务端登出失败", "file": "src/stores/auth.ts", "line": 150, "context": "// 即使服务端登出失败，也要清除本地状态"}, {"text": "也要清除本地状态", "file": "src/stores/auth.ts", "line": 150, "context": "// 即使服务端登出失败，也要清除本地状态"}, {"text": "登出请求失败", "file": "src/stores/auth.ts", "line": 151, "context": "console.error('登出请求失败:', err)"}, {"text": "刷新", "file": "src/stores/auth.ts", "line": 157, "context": "// 刷新token"}, {"text": "清除错误", "file": "src/stores/auth.ts", "line": 175, "context": "// 清除错误"}, {"text": "状态", "file": "src/stores/auth.ts", "line": 181, "context": "// 状态"}, {"text": "计算属性", "file": "src/stores/auth.ts", "line": 187, "context": "// 计算属性"}, {"text": "方法", "file": "src/stores/auth.ts", "line": 191, "context": "// 方法"}, {"text": "用户相关类型", "file": "src/types/index.ts", "line": 1, "context": "// 用户相关类型"}, {"text": "宠物相关类型", "file": "src/types/index.ts", "line": 32, "context": "// 宠物相关类型"}, {"text": "帖子相关类型", "file": "src/types/index.ts", "line": 58, "context": "// 帖子相关类型"}, {"text": "线索相关类型", "file": "src/types/index.ts", "line": 84, "context": "// 线索相关类型"}, {"text": "地图相关类型", "file": "src/types/index.ts", "line": 111, "context": "// 地图相关类型"}, {"text": "响应类型", "file": "src/types/index.ts", "line": 118, "context": "// API响应类型"}, {"text": "搜索和筛选类型", "file": "src/types/index.ts", "line": 140, "context": "// 搜索和筛选类型"}, {"text": "创建", "file": "src/utils/api.ts", "line": 4, "context": "// 创建axios实例"}, {"text": "实例", "file": "src/utils/api.ts", "line": 4, "context": "// 创建axios实例"}, {"text": "请求拦截器", "file": "src/utils/api.ts", "line": 13, "context": "// 请求拦截器 - 添加token"}, {"text": "添加", "file": "src/utils/api.ts", "line": 13, "context": "// 请求拦截器 - 添加token"}, {"text": "响应拦截器", "file": "src/utils/api.ts", "line": 27, "context": "// 响应拦截器 - 处理错误"}, {"text": "处理错误", "file": "src/utils/api.ts", "line": 27, "context": "// 响应拦截器 - 处理错误"}, {"text": "过期或无效", "file": "src/utils/api.ts", "line": 34, "context": "// Token过期或无效，清除本地存储并跳转到登录页"}, {"text": "清除本地存储并跳转到登录页", "file": "src/utils/api.ts", "line": 34, "context": "// Token过期或无效，清除本地存储并跳转到登录页"}, {"text": "请求封装函数", "file": "src/utils/api.ts", "line": 43, "context": "// API请求封装函数"}, {"text": "请求", "file": "src/utils/api.ts", "line": 45, "context": "// GET请求"}, {"text": "请求失败", "file": "src/utils/api.ts", "line": 51, "context": "throw error.response?.data || { success: false, message: '请求失败' }"}, {"text": "请求", "file": "src/utils/api.ts", "line": 55, "context": "// POST请求"}, {"text": "请求失败", "file": "src/utils/api.ts", "line": 61, "context": "throw error.response?.data || { success: false, message: '请求失败' }"}, {"text": "请求", "file": "src/utils/api.ts", "line": 65, "context": "// PUT请求"}, {"text": "请求失败", "file": "src/utils/api.ts", "line": 71, "context": "throw error.response?.data || { success: false, message: '请求失败' }"}, {"text": "请求", "file": "src/utils/api.ts", "line": 75, "context": "// PATCH请求"}, {"text": "请求失败", "file": "src/utils/api.ts", "line": 81, "context": "throw error.response?.data || { success: false, message: '请求失败' }"}, {"text": "请求", "file": "src/utils/api.ts", "line": 85, "context": "// DELETE请求"}, {"text": "请求失败", "file": "src/utils/api.ts", "line": 91, "context": "throw error.response?.data || { success: false, message: '请求失败' }"}, {"text": "文件上传请求", "file": "src/utils/api.ts", "line": 95, "context": "// 文件上传请求"}, {"text": "上传失败", "file": "src/utils/api.ts", "line": 107, "context": "throw error.response?.data || { success: false, message: '上传失败' }"}, {"text": "认证路由守卫", "file": "src/utils/auth-guard.ts", "line": 5, "context": "* 认证路由守卫"}, {"text": "需要认证的路由", "file": "src/utils/auth-guard.ts", "line": 14, "context": "// 需要认证的路由"}, {"text": "只允许游客访问的路由", "file": "src/utils/auth-guard.ts", "line": 17, "context": "// 只允许游客访问的路由（如登录、注册页）"}, {"text": "如登录", "file": "src/utils/auth-guard.ts", "line": 17, "context": "// 只允许游客访问的路由（如登录、注册页）"}, {"text": "注册页", "file": "src/utils/auth-guard.ts", "line": 17, "context": "// 只允许游客访问的路由（如登录、注册页）"}, {"text": "需要认证但未登录", "file": "src/utils/auth-guard.ts", "line": 21, "context": "// 需要认证但未登录，跳转到登录页"}, {"text": "跳转到登录页", "file": "src/utils/auth-guard.ts", "line": 21, "context": "// 需要认证但未登录，跳转到登录页"}, {"text": "已登录用户访问游客页面", "file": "src/utils/auth-guard.ts", "line": 27, "context": "// 已登录用户访问游客页面，跳转到首页"}, {"text": "跳转到首页", "file": "src/utils/auth-guard.ts", "line": 27, "context": "// 已登录用户访问游客页面，跳转到首页"}, {"text": "允许访问", "file": "src/utils/auth-guard.ts", "line": 30, "context": "// 允许访问"}, {"text": "管理员路由守卫", "file": "src/utils/auth-guard.ts", "line": 36, "context": "* 管理员路由守卫"}, {"text": "检查是否需要管理员权限", "file": "src/utils/auth-guard.ts", "line": 45, "context": "// 检查是否需要管理员权限"}, {"text": "未登录", "file": "src/utils/auth-guard.ts", "line": 50, "context": "// 未登录，跳转到登录页"}, {"text": "跳转到登录页", "file": "src/utils/auth-guard.ts", "line": 50, "context": "// 未登录，跳转到登录页"}, {"text": "这里可以添加管理员权限检查逻辑", "file": "src/utils/auth-guard.ts", "line": 56, "context": "// 这里可以添加管理员权限检查逻辑"}, {"text": "目前简单允许所有已登录用户访问", "file": "src/utils/auth-guard.ts", "line": 57, "context": "// 目前简单允许所有已登录用户访问"}, {"text": "配置", "file": "src/utils/helpers.ts", "line": 5, "context": "// 配置dayjs"}, {"text": "格式化日期时间", "file": "src/utils/helpers.ts", "line": 10, "context": "* 格式化日期时间"}, {"text": "获取相对时间", "file": "src/utils/helpers.ts", "line": 17, "context": "* 获取相对时间"}, {"text": "验证邮箱格式", "file": "src/utils/helpers.ts", "line": 24, "context": "* 验证邮箱格式"}, {"text": "验证手机号格式", "file": "src/utils/helpers.ts", "line": 32, "context": "* 验证手机号格式（香港）"}, {"text": "香港", "file": "src/utils/helpers.ts", "line": 32, "context": "* 验证手机号格式（香港）"}, {"text": "文件大小格式化", "file": "src/utils/helpers.ts", "line": 40, "context": "* 文件大小格式化"}, {"text": "验证图片文件类型", "file": "src/utils/helpers.ts", "line": 51, "context": "* 验证图片文件类型"}, {"text": "验证文件大小", "file": "src/utils/helpers.ts", "line": 59, "context": "* 验证文件大小（默认最大5MB）"}, {"text": "默认最大", "file": "src/utils/helpers.ts", "line": 59, "context": "* 验证文件大小（默认最大5MB）"}, {"text": "压缩图片", "file": "src/utils/helpers.ts", "line": 67, "context": "* 压缩图片"}, {"text": "计算新尺寸", "file": "src/utils/helpers.ts", "line": 76, "context": "// 计算新尺寸"}, {"text": "绘制压缩后的图片", "file": "src/utils/helpers.ts", "line": 86, "context": "// 绘制压缩后的图片"}, {"text": "防抖函数", "file": "src/utils/helpers.ts", "line": 107, "context": "* 防抖函数"}, {"text": "节流函数", "file": "src/utils/helpers.ts", "line": 121, "context": "* 节流函数"}, {"text": "生成随机", "file": "src/utils/helpers.ts", "line": 138, "context": "* 生成随机ID"}, {"text": "深拷贝对象", "file": "src/utils/helpers.ts", "line": 145, "context": "* 深拷贝对象"}, {"text": "获取文件预览", "file": "src/utils/helpers.ts", "line": 152, "context": "* 获取文件预览URL"}, {"text": "清理文件预览", "file": "src/utils/helpers.ts", "line": 159, "context": "* 清理文件预览URL"}, {"text": "复制文本到剪贴板", "file": "src/utils/helpers.ts", "line": 166, "context": "* 复制文本到剪贴板"}, {"text": "降级方案", "file": "src/utils/helpers.ts", "line": 173, "context": "// 降级方案"}, {"text": "获取完整的图片", "file": "src/utils/helpers.ts", "line": 185, "context": "* 获取完整的图片URL"}, {"text": "相对图片", "file": "src/utils/helpers.ts", "line": 186, "context": "* @param imageUrl - 相对图片URL（如：/uploads/pets/xxx.png）"}, {"text": "完整的图片", "file": "src/utils/helpers.ts", "line": 187, "context": "* @returns 完整的图片URL，如果输入为空则返回空字符串"}, {"text": "如果输入为空则返回空字符串", "file": "src/utils/helpers.ts", "line": 187, "context": "* @returns 完整的图片URL，如果输入为空则返回空字符串"}, {"text": "如果已经是完整", "file": "src/utils/helpers.ts", "line": 192, "context": "// 如果已经是完整URL，直接返回"}, {"text": "直接返回", "file": "src/utils/helpers.ts", "line": 192, "context": "// 如果已经是完整URL，直接返回"}, {"text": "在开发环境中", "file": "src/utils/helpers.ts", "line": 197, "context": "// 在开发环境中，Vite代理会处理 /uploads 路径"}, {"text": "代理会处理", "file": "src/utils/helpers.ts", "line": 197, "context": "// 在开发环境中，Vite代理会处理 /uploads 路径"}, {"text": "路径", "file": "src/utils/helpers.ts", "line": 197, "context": "// 在开发环境中，Vite代理会处理 /uploads 路径"}, {"text": "在生产环境中", "file": "src/utils/helpers.ts", "line": 198, "context": "// 在生产环境中，需要完整的URL"}, {"text": "需要完整的", "file": "src/utils/helpers.ts", "line": 198, "context": "// 在生产环境中，需要完整的URL"}, {"text": "开发环境使用代理", "file": "src/utils/helpers.ts", "line": 200, "context": "return imageUrl // 开发环境使用代理"}, {"text": "生产环境需要完整", "file": "src/utils/helpers.ts", "line": 202, "context": "// 生产环境需要完整URL"}, {"text": "页面标题", "file": "src/views/CreatePostView.vue", "line": 4, "context": "<!-- 页面标题 -->"}, {"text": "发布走失信息", "file": "src/views/CreatePostView.vue", "line": 6, "context": "<h1 class=\"text-3xl font-bold text-gray-900\">发布走失信息</h1>"}, {"text": "请详细填写宠物信息", "file": "src/views/CreatePostView.vue", "line": 7, "context": "<p class=\"mt-2 text-gray-600\">请详细填写宠物信息，帮助更多人识别您的宠物</p>"}, {"text": "帮助更多人识别您的宠物", "file": "src/views/CreatePostView.vue", "line": 7, "context": "<p class=\"mt-2 text-gray-600\">请详细填写宠物信息，帮助更多人识别您的宠物</p>"}, {"text": "步骤指示器", "file": "src/views/CreatePostView.vue", "line": 10, "context": "<!-- 步骤指示器 -->"}, {"text": "表单内容", "file": "src/views/CreatePostView.vue", "line": 30, "context": "<!-- 表单内容 -->"}, {"text": "步骤", "file": "src/views/CreatePostView.vue", "line": 33, "context": "<!-- 步骤1: 宠物基本信息 -->"}, {"text": "宠物基本信息", "file": "src/views/CreatePostView.vue", "line": 33, "context": "<!-- 步骤1: 宠物基本信息 -->"}, {"text": "宠物基本信息", "file": "src/views/CreatePostView.vue", "line": 35, "context": "<h2 class=\"text-lg font-medium text-gray-900 mb-4\">宠物基本信息</h2>"}, {"text": "选择模式", "file": "src/views/CreatePostView.vue", "line": 37, "context": "<!-- 选择模式 -->"}, {"text": "选择宠物信息方式", "file": "src/views/CreatePostView.vue", "line": 39, "context": "<h3 class=\"text-sm font-medium text-gray-900 mb-3\">选择宠物信息方式</h3>"}, {"text": "选择已有宠物", "file": "src/views/CreatePostView.vue", "line": 48, "context": "<span class=\"ml-2 text-sm text-gray-700\">选择已有宠物</span>"}, {"text": "添加新宠物", "file": "src/views/CreatePostView.vue", "line": 57, "context": "<span class=\"ml-2 text-sm text-gray-700\">添加新宠物</span>"}, {"text": "选择已有宠物", "file": "src/views/CreatePostView.vue", "line": 62, "context": "<!-- 选择已有宠物 -->"}, {"text": "加载宠物列表中", "file": "src/views/CreatePostView.vue", "line": 66, "context": "<p class=\"mt-2 text-sm text-gray-600\">加载宠物列表中...</p>"}, {"text": "还没有宠物信息", "file": "src/views/CreatePostView.vue", "line": 73, "context": "<h3 class=\"mt-2 text-sm font-medium text-gray-900\">还没有宠物信息</h3>"}, {"text": "请先添加宠物信息", "file": "src/views/CreatePostView.vue", "line": 74, "context": "<p class=\"mt-1 text-sm text-gray-500\">请先添加宠物信息，或选择\"添加新宠物\"</p>"}, {"text": "或选择", "file": "src/views/CreatePostView.vue", "line": 74, "context": "<p class=\"mt-1 text-sm text-gray-500\">请先添加宠物信息，或选择\"添加新宠物\"</p>"}, {"text": "添加新宠物", "file": "src/views/CreatePostView.vue", "line": 74, "context": "<p class=\"mt-1 text-sm text-gray-500\">请先添加宠物信息，或选择\"添加新宠物\"</p>"}, {"text": "添加新宠物", "file": "src/views/CreatePostView.vue", "line": 81, "context": "添加新宠物"}, {"text": "未命名", "file": "src/views/CreatePostView.vue", "line": 109, "context": "<h4 class=\"text-sm font-medium text-gray-900 truncate\">{{ pet.name || '未命名' }}</h4>"}, {"text": "添加新宠物表单", "file": "src/views/CreatePostView.vue", "line": 123, "context": "<!-- 添加新宠物表单 -->"}, {"text": "宠物名字", "file": "src/views/CreatePostView.vue", "line": 125, "context": "<!-- 宠物名字 -->"}, {"text": "宠物名字", "file": "src/views/CreatePostView.vue", "line": 128, "context": "宠物名字 <span class=\"text-red-500\">*</span>"}, {"text": "请输入宠物名字", "file": "src/views/CreatePostView.vue", "line": 136, "context": "placeholder=\"请输入宠物名字\""}, {"text": "宠物品种", "file": "src/views/CreatePostView.vue", "line": 140, "context": "<!-- 宠物品种 -->"}, {"text": "宠物品种", "file": "src/views/CreatePostView.vue", "line": 143, "context": "宠物品种 <span class=\"text-red-500\">*</span>"}, {"text": "请选择品种", "file": "src/views/CreatePostView.vue", "line": 151, "context": "<option value=\"\">请选择品种</option>"}, {"text": "品种详情", "file": "src/views/CreatePostView.vue", "line": 158, "context": "<!-- 品种详情 -->"}, {"text": "具体品种", "file": "src/views/CreatePostView.vue", "line": 161, "context": "具体品种（可选）"}, {"text": "可选", "file": "src/views/CreatePostView.vue", "line": 161, "context": "具体品种（可选）"}, {"text": "金毛", "file": "src/views/CreatePostView.vue", "line": 168, "context": "placeholder=\"如：金毛、波斯猫等\""}, {"text": "波斯猫等", "file": "src/views/CreatePostView.vue", "line": 168, "context": "placeholder=\"如：金毛、波斯猫等\""}, {"text": "主要毛色", "file": "src/views/CreatePostView.vue", "line": 172, "context": "<!-- 主要毛色 -->"}, {"text": "主要毛色", "file": "src/views/CreatePostView.vue", "line": 175, "context": "主要毛色 <span class=\"text-red-500\">*</span>"}, {"text": "请选择毛色", "file": "src/views/CreatePostView.vue", "line": 183, "context": "<option value=\"\">请选择毛色</option>"}, {"text": "性别", "file": "src/views/CreatePostView.vue", "line": 190, "context": "<!-- 性别 -->"}, {"text": "性别", "file": "src/views/CreatePostView.vue", "line": 193, "context": "性别 <span class=\"text-red-500\">*</span>"}, {"text": "请选择性别", "file": "src/views/CreatePostView.vue", "line": 201, "context": "<option value=\"\">请选择性别</option>"}, {"text": "年龄", "file": "src/views/CreatePostView.vue", "line": 208, "context": "<!-- 年龄 -->"}, {"text": "年龄", "file": "src/views/CreatePostView.vue", "line": 211, "context": "年龄（可选）"}, {"text": "可选", "file": "src/views/CreatePostView.vue", "line": 211, "context": "年龄（可选）"}, {"text": "请输入年龄", "file": "src/views/CreatePostView.vue", "line": 220, "context": "placeholder=\"请输入年龄\""}, {"text": "宠物描述", "file": "src/views/CreatePostView.vue", "line": 225, "context": "<!-- 宠物描述 -->"}, {"text": "外观特征描述", "file": "src/views/CreatePostView.vue", "line": 228, "context": "外观特征描述（可选）"}, {"text": "可选", "file": "src/views/CreatePostView.vue", "line": 228, "context": "外观特征描述（可选）"}, {"text": "请描述宠物的外观特征", "file": "src/views/CreatePostView.vue", "line": 235, "context": "placeholder=\"请描述宠物的外观特征，如体型、特殊标记等\""}, {"text": "如体型", "file": "src/views/CreatePostView.vue", "line": 235, "context": "placeholder=\"请描述宠物的外观特征，如体型、特殊标记等\""}, {"text": "特殊标记等", "file": "src/views/CreatePostView.vue", "line": 235, "context": "placeholder=\"请描述宠物的外观特征，如体型、特殊标记等\""}, {"text": "宠物照片", "file": "src/views/CreatePostView.vue", "line": 239, "context": "<!-- 宠物照片 -->"}, {"text": "宠物照片", "file": "src/views/CreatePostView.vue", "line": 242, "context": "label=\"宠物照片\""}, {"text": "请上传清晰的宠物照片", "file": "src/views/CreatePostView.vue", "line": 244, "context": "help-text=\"请上传清晰的宠物照片，有助于他人识别\""}, {"text": "有助于他人识别", "file": "src/views/CreatePostView.vue", "line": 244, "context": "help-text=\"请上传清晰的宠物照片，有助于他人识别\""}, {"text": "步骤", "file": "src/views/CreatePostView.vue", "line": 248, "context": "<!-- 步骤2: 走失信息 -->"}, {"text": "走失信息", "file": "src/views/CreatePostView.vue", "line": 248, "context": "<!-- 步骤2: 走失信息 -->"}, {"text": "走失信息", "file": "src/views/CreatePostView.vue", "line": 250, "context": "<h2 class=\"text-lg font-medium text-gray-900 mb-4\">走失信息</h2>"}, {"text": "走失时间", "file": "src/views/CreatePostView.vue", "line": 252, "context": "<!-- 走失时间 -->"}, {"text": "走失时间", "file": "src/views/CreatePostView.vue", "line": 255, "context": "走失时间 <span class=\"text-red-500\">*</span>"}, {"text": "走失地点", "file": "src/views/CreatePostView.vue", "line": 266, "context": "<!-- 走失地点 -->"}, {"text": "走失地点", "file": "src/views/CreatePostView.vue", "line": 270, "context": "label=\"走失地点\""}, {"text": "请输入走失地点进行搜索", "file": "src/views/CreatePostView.vue", "line": 271, "context": "placeholder=\"请输入走失地点进行搜索\""}, {"text": "联系方式", "file": "src/views/CreatePostView.vue", "line": 276, "context": "<!-- 联系方式 -->"}, {"text": "联系方式", "file": "src/views/CreatePostView.vue", "line": 279, "context": "联系方式（可选）"}, {"text": "可选", "file": "src/views/CreatePostView.vue", "line": 279, "context": "联系方式（可选）"}, {"text": "如需要", "file": "src/views/CreatePostView.vue", "line": 286, "context": "placeholder=\"如需要，可以提供额外的联系方式\""}, {"text": "可以提供额外的联系方式", "file": "src/views/CreatePostView.vue", "line": 286, "context": "placeholder=\"如需要，可以提供额外的联系方式\""}, {"text": "系统会自动使用您的注册邮箱作为联系方式", "file": "src/views/CreatePostView.vue", "line": 289, "context": "系统会自动使用您的注册邮箱作为联系方式，此处可提供额外联系方式"}, {"text": "此处可提供额外联系方式", "file": "src/views/CreatePostView.vue", "line": 289, "context": "系统会自动使用您的注册邮箱作为联系方式，此处可提供额外联系方式"}, {"text": "视频链接", "file": "src/views/CreatePostView.vue", "line": 293, "context": "<!-- 视频链接 -->"}, {"text": "视频链接", "file": "src/views/CreatePostView.vue", "line": 296, "context": "视频链接（可选）"}, {"text": "可选", "file": "src/views/CreatePostView.vue", "line": 296, "context": "视频链接（可选）"}, {"text": "如有宠物视频", "file": "src/views/CreatePostView.vue", "line": 303, "context": "placeholder=\"如有宠物视频，请提供链接\""}, {"text": "请提供链接", "file": "src/views/CreatePostView.vue", "line": 303, "context": "placeholder=\"如有宠物视频，请提供链接\""}, {"text": "错误提示", "file": "src/views/CreatePostView.vue", "line": 308, "context": "<!-- 错误提示 -->"}, {"text": "操作按钮", "file": "src/views/CreatePostView.vue", "line": 324, "context": "<!-- 操作按钮 -->"}, {"text": "上一步", "file": "src/views/CreatePostView.vue", "line": 332, "context": "上一步"}, {"text": "取消", "file": "src/views/CreatePostView.vue", "line": 341, "context": "取消"}, {"text": "下一步", "file": "src/views/CreatePostView.vue", "line": 351, "context": "下一步"}, {"text": "发布中", "file": "src/views/CreatePostView.vue", "line": 362, "context": "发布中..."}, {"text": "发布信息", "file": "src/views/CreatePostView.vue", "line": 364, "context": "<span v-else>发布信息</span>"}, {"text": "步骤定义", "file": "src/views/CreatePostView.vue", "line": 390, "context": "// 步骤定义"}, {"text": "宠物信息", "file": "src/views/CreatePostView.vue", "line": 392, "context": "{ id: 1, name: '宠物信息' },"}, {"text": "走失信息", "file": "src/views/CreatePostView.vue", "line": 393, "context": "{ id: 2, name: '走失信息' },"}, {"text": "宠物选择模式", "file": "src/views/CreatePostView.vue", "line": 400, "context": "// 宠物选择模式"}, {"text": "表单数据", "file": "src/views/CreatePostView.vue", "line": 406, "context": "// 表单数据"}, {"text": "计算属性", "file": "src/views/CreatePostView.vue", "line": 428, "context": "// 计算属性"}, {"text": "方法", "file": "src/views/CreatePostView.vue", "line": 468, "context": "// 方法"}, {"text": "获取用户宠物列表", "file": "src/views/CreatePostView.vue", "line": 481, "context": "// 获取用户宠物列表"}, {"text": "如果有宠物", "file": "src/views/CreatePostView.vue", "line": 489, "context": "// 如果有宠物，默认选择第一个"}, {"text": "默认选择第一个", "file": "src/views/CreatePostView.vue", "line": 489, "context": "// 如果有宠物，默认选择第一个"}, {"text": "获取宠物列表失败", "file": "src/views/CreatePostView.vue", "line": 495, "context": "console.error('获取宠物列表失败:', err)"}, {"text": "如果获取失败", "file": "src/views/CreatePostView.vue", "line": 496, "context": "// 如果获取失败，切换到新建模式"}, {"text": "切换到新建模式", "file": "src/views/CreatePostView.vue", "line": 496, "context": "// 如果获取失败，切换到新建模式"}, {"text": "选择宠物", "file": "src/views/CreatePostView.vue", "line": 503, "context": "// 选择宠物"}, {"text": "使用已选择的宠物", "file": "src/views/CreatePostView.vue", "line": 518, "context": "// 使用已选择的宠物"}, {"text": "请选择一个宠物", "file": "src/views/CreatePostView.vue", "line": 520, "context": "throw new Error('请选择一个宠物')"}, {"text": "创建新宠物", "file": "src/views/CreatePostView.vue", "line": 524, "context": "// 创建新宠物"}, {"text": "创建宠物信息失败", "file": "src/views/CreatePostView.vue", "line": 528, "context": "throw new Error(petResponse.message || '创建宠物信息失败')"}, {"text": "创建帖子", "file": "src/views/CreatePostView.vue", "line": 533, "context": "// 创建帖子"}, {"text": "创建帖子失败", "file": "src/views/CreatePostView.vue", "line": 545, "context": "throw new Error(postResponse.message || '创建帖子失败')"}, {"text": "成功后跳转到仪表板的我的帖子页面", "file": "src/views/CreatePostView.vue", "line": 548, "context": "// 成功后跳转到仪表板的我的帖子页面"}, {"text": "发布失败", "file": "src/views/CreatePostView.vue", "line": 552, "context": "console.error('发布失败:', err)"}, {"text": "发布失败", "file": "src/views/CreatePostView.vue", "line": 553, "context": "error.value = err.message || '发布失败，请稍后重试'"}, {"text": "请稍后重试", "file": "src/views/CreatePostView.vue", "line": 553, "context": "error.value = err.message || '发布失败，请稍后重试'"}, {"text": "检查用户是否已登录", "file": "src/views/CreatePostView.vue", "line": 560, "context": "// 检查用户是否已登录"}, {"text": "获取用户宠物列表", "file": "src/views/CreatePostView.vue", "line": 566, "context": "// 获取用户宠物列表"}, {"text": "如果没有宠物", "file": "src/views/CreatePostView.vue", "line": 569, "context": "// 如果没有宠物，默认切换到新建模式"}, {"text": "默认切换到新建模式", "file": "src/views/CreatePostView.vue", "line": 569, "context": "// 如果没有宠物，默认切换到新建模式"}, {"text": "导航栏", "file": "src/views/DashboardView.vue", "line": 3, "context": "<!-- 导航栏 -->"}, {"text": "走失宠物协寻平台", "file": "src/views/DashboardView.vue", "line": 9, "context": "走失宠物协寻平台"}, {"text": "浏览信息", "file": "src/views/DashboardView.vue", "line": 18, "context": "浏览信息"}, {"text": "发布走失信息", "file": "src/views/DashboardView.vue", "line": 24, "context": "发布走失信息"}, {"text": "登出", "file": "src/views/DashboardView.vue", "line": 30, "context": "登出"}, {"text": "页面标题", "file": "src/views/DashboardView.vue", "line": 38, "context": "<!-- 页面标题 -->"}, {"text": "我的账户", "file": "src/views/DashboardView.vue", "line": 40, "context": "<h1 class=\"text-3xl font-bold text-gray-900\">我的账户</h1>"}, {"text": "管理您的宠物信息和寻宠帖子", "file": "src/views/DashboardView.vue", "line": 41, "context": "<p class=\"mt-2 text-gray-600\">管理您的宠物信息和寻宠帖子</p>"}, {"text": "侧边栏导航", "file": "src/views/DashboardView.vue", "line": 44, "context": "<!-- 侧边栏导航 -->"}, {"text": "概览", "file": "src/views/DashboardView.vue", "line": 59, "context": "概览"}, {"text": "我的帖子", "file": "src/views/DashboardView.vue", "line": 72, "context": "我的帖子"}, {"text": "我的宠物", "file": "src/views/DashboardView.vue", "line": 85, "context": "我的宠物"}, {"text": "个人资料", "file": "src/views/DashboardView.vue", "line": 98, "context": "个人资料"}, {"text": "主要内容区域", "file": "src/views/DashboardView.vue", "line": 105, "context": "<!-- 主要内容区域 -->"}, {"text": "概览", "file": "src/views/DashboardView.vue", "line": 107, "context": "<!-- 概览 -->"}, {"text": "我的帖子", "file": "src/views/DashboardView.vue", "line": 112, "context": "<!-- 我的帖子 -->"}, {"text": "我的宠物", "file": "src/views/DashboardView.vue", "line": 117, "context": "<!-- 我的宠物 -->"}, {"text": "个人资料", "file": "src/views/DashboardView.vue", "line": 122, "context": "<!-- 个人资料 -->"}, {"text": "状态", "file": "src/views/DashboardView.vue", "line": 145, "context": "// 状态"}, {"text": "方法", "file": "src/views/DashboardView.vue", "line": 148, "context": "// 方法"}, {"text": "检查用户是否已登录", "file": "src/views/DashboardView.vue", "line": 155, "context": "// 检查用户是否已登录"}, {"text": "根据查询参数设置活动标签页", "file": "src/views/DashboardView.vue", "line": 161, "context": "// 根据查询参数设置活动标签页"}, {"text": "导航栏", "file": "src/views/HomeView.vue", "line": 3, "context": "<!-- 导航栏 -->"}, {"text": "走失宠物协寻平台", "file": "src/views/HomeView.vue", "line": 8, "context": "<h1 class=\"text-xl font-bold text-gray-900\">走失宠物协寻平台</h1>"}, {"text": "欢迎", "file": "src/views/HomeView.vue", "line": 13, "context": "<span class=\"text-gray-700\">欢迎，{{ authStore.user?.username }}</span>"}, {"text": "登出", "file": "src/views/HomeView.vue", "line": 18, "context": "登出"}, {"text": "登录", "file": "src/views/HomeView.vue", "line": 26, "context": "登录"}, {"text": "注册", "file": "src/views/HomeView.vue", "line": 32, "context": "注册"}, {"text": "主要内容", "file": "src/views/HomeView.vue", "line": 40, "context": "<!-- 主要内容 -->"}, {"text": "帮助走失的宠物回家", "file": "src/views/HomeView.vue", "line": 45, "context": "帮助走失的宠物回家"}, {"text": "快速发布走失信息", "file": "src/views/HomeView.vue", "line": 48, "context": "快速发布走失信息，让更多人帮助寻找您的宠物"}, {"text": "让更多人帮助寻找您的宠物", "file": "src/views/HomeView.vue", "line": 48, "context": "快速发布走失信息，让更多人帮助寻找您的宠物"}, {"text": "发布走失信息", "file": "src/views/HomeView.vue", "line": 56, "context": "发布走失信息"}, {"text": "浏览寻宠信息", "file": "src/views/HomeView.vue", "line": 62, "context": "浏览寻宠信息"}, {"text": "功能介绍", "file": "src/views/HomeView.vue", "line": 67, "context": "<!-- 功能介绍 -->"}, {"text": "一键通报", "file": "src/views/HomeView.vue", "line": 75, "context": "<h3 class=\"mt-4 text-lg font-medium text-gray-900\">一键通报</h3>"}, {"text": "快速填写宠物信息", "file": "src/views/HomeView.vue", "line": 77, "context": "快速填写宠物信息，2分钟内完成走失通报"}, {"text": "分钟内完成走失通报", "file": "src/views/HomeView.vue", "line": 77, "context": "快速填写宠物信息，2分钟内完成走失通报"}, {"text": "智能搜索", "file": "src/views/HomeView.vue", "line": 87, "context": "<h3 class=\"mt-4 text-lg font-medium text-gray-900\">智能搜索</h3>"}, {"text": "多维度筛选", "file": "src/views/HomeView.vue", "line": 89, "context": "多维度筛选，快速找到相关的走失信息"}, {"text": "快速找到相关的走失信息", "file": "src/views/HomeView.vue", "line": 89, "context": "多维度筛选，快速找到相关的走失信息"}, {"text": "社交分享", "file": "src/views/HomeView.vue", "line": 99, "context": "<h3 class=\"mt-4 text-lg font-medium text-gray-900\">社交分享</h3>"}, {"text": "一键分享到各大社交平台", "file": "src/views/HomeView.vue", "line": 101, "context": "一键分享到各大社交平台，扩大寻找范围"}, {"text": "扩大寻找范围", "file": "src/views/HomeView.vue", "line": 101, "context": "一键分享到各大社交平台，扩大寻找范围"}, {"text": "处理登出", "file": "src/views/HomeView.vue", "line": 117, "context": "// 处理登出"}, {"text": "登录您的账户", "file": "src/views/LoginView.vue", "line": 6, "context": "登录您的账户"}, {"text": "还没有账户", "file": "src/views/LoginView.vue", "line": 9, "context": "还没有账户？"}, {"text": "立即注册", "file": "src/views/LoginView.vue", "line": 14, "context": "立即注册"}, {"text": "邮箱地址", "file": "src/views/LoginView.vue", "line": 22, "context": "<label for=\"email\" class=\"sr-only\">邮箱地址</label>"}, {"text": "邮箱地址", "file": "src/views/LoginView.vue", "line": 31, "context": "placeholder=\"邮箱地址\""}, {"text": "密码", "file": "src/views/LoginView.vue", "line": 35, "context": "<label for=\"password\" class=\"sr-only\">密码</label>"}, {"text": "密码", "file": "src/views/LoginView.vue", "line": 44, "context": "placeholder=\"密码\""}, {"text": "记住我", "file": "src/views/LoginView.vue", "line": 59, "context": "记住我"}, {"text": "忘记密码", "file": "src/views/LoginView.vue", "line": 65, "context": "忘记密码？"}, {"text": "错误提示", "file": "src/views/LoginView.vue", "line": 70, "context": "<!-- 错误提示 -->"}, {"text": "登录中", "file": "src/views/LoginView.vue", "line": 93, "context": "{{ authStore.isLoading ? '登录中...' : '登录' }}"}, {"text": "登录", "file": "src/views/LoginView.vue", "line": 93, "context": "{{ authStore.isLoading ? '登录中...' : '登录' }}"}, {"text": "表单数据", "file": "src/views/LoginView.vue", "line": 112, "context": "// 表单数据"}, {"text": "处理登录", "file": "src/views/LoginView.vue", "line": 120, "context": "// 处理登录"}, {"text": "清除之前的错误", "file": "src/views/LoginView.vue", "line": 122, "context": "// 清除之前的错误"}, {"text": "基础验证", "file": "src/views/LoginView.vue", "line": 125, "context": "// 基础验证"}, {"text": "登录成功", "file": "src/views/LoginView.vue", "line": 137, "context": "// 登录成功，跳转到目标页面或首页"}, {"text": "跳转到目标页面或首页", "file": "src/views/LoginView.vue", "line": 137, "context": "// 登录成功，跳转到目标页面或首页"}, {"text": "清除之前的错误", "file": "src/views/LoginView.vue", "line": 144, "context": "// 清除之前的错误"}, {"text": "导航栏", "file": "src/views/PostDetailView.vue", "line": 3, "context": "<!-- 导航栏 -->"}, {"text": "走失宠物协寻平台", "file": "src/views/PostDetailView.vue", "line": 9, "context": "走失宠物协寻平台"}, {"text": "返回列表", "file": "src/views/PostDetailView.vue", "line": 18, "context": "返回列表"}, {"text": "加载状态", "file": "src/views/PostDetailView.vue", "line": 26, "context": "<!-- 加载状态 -->"}, {"text": "帖子详情", "file": "src/views/PostDetailView.vue", "line": 31, "context": "<!-- 帖子详情 -->"}, {"text": "帖子状态", "file": "src/views/PostDetailView.vue", "line": 33, "context": "<!-- 帖子状态 -->"}, {"text": "寻找", "file": "src/views/PostDetailView.vue", "line": 38, "context": "寻找 {{ post.pet?.name }}"}, {"text": "发布于", "file": "src/views/PostDetailView.vue", "line": 41, "context": "发布于 {{ formatDate(post.created_at) }}"}, {"text": "宠物信息", "file": "src/views/PostDetailView.vue", "line": 59, "context": "<!-- 宠物信息 -->"}, {"text": "宠物信息", "file": "src/views/PostDetailView.vue", "line": 61, "context": "<h2 class=\"text-lg font-medium text-gray-900 mb-4\">宠物信息</h2>"}, {"text": "宠物照片", "file": "src/views/PostDetailView.vue", "line": 64, "context": "<!-- 宠物照片 -->"}, {"text": "宠物详细信息", "file": "src/views/PostDetailView.vue", "line": 82, "context": "<!-- 宠物详细信息 -->"}, {"text": "名字", "file": "src/views/PostDetailView.vue", "line": 86, "context": "<dt class=\"text-sm font-medium text-gray-500\">名字</dt>"}, {"text": "品种", "file": "src/views/PostDetailView.vue", "line": 90, "context": "<dt class=\"text-sm font-medium text-gray-500\">品种</dt>"}, {"text": "毛色", "file": "src/views/PostDetailView.vue", "line": 94, "context": "<dt class=\"text-sm font-medium text-gray-500\">毛色</dt>"}, {"text": "性别", "file": "src/views/PostDetailView.vue", "line": 98, "context": "<dt class=\"text-sm font-medium text-gray-500\">性别</dt>"}, {"text": "具体品种", "file": "src/views/PostDetailView.vue", "line": 104, "context": "<dt class=\"text-sm font-medium text-gray-500\">具体品种</dt>"}, {"text": "年龄", "file": "src/views/PostDetailView.vue", "line": 108, "context": "<dt class=\"text-sm font-medium text-gray-500\">年龄</dt>"}, {"text": "外观特征", "file": "src/views/PostDetailView.vue", "line": 114, "context": "<dt class=\"text-sm font-medium text-gray-500\">外观特征</dt>"}, {"text": "走失信息", "file": "src/views/PostDetailView.vue", "line": 121, "context": "<!-- 走失信息 -->"}, {"text": "走失信息", "file": "src/views/PostDetailView.vue", "line": 123, "context": "<h2 class=\"text-lg font-medium text-gray-900 mb-4\">走失信息</h2>"}, {"text": "走失时间", "file": "src/views/PostDetailView.vue", "line": 127, "context": "<dt class=\"text-sm font-medium text-gray-500\">走失时间</dt>"}, {"text": "走失地点", "file": "src/views/PostDetailView.vue", "line": 131, "context": "<dt class=\"text-sm font-medium text-gray-500\">走失地点</dt>"}, {"text": "联系方式", "file": "src/views/PostDetailView.vue", "line": 135, "context": "<dt class=\"text-sm font-medium text-gray-500\">联系方式</dt>"}, {"text": "发布者", "file": "src/views/PostDetailView.vue", "line": 139, "context": "<dt class=\"text-sm font-medium text-gray-500\">发布者</dt>"}, {"text": "视频链接", "file": "src/views/PostDetailView.vue", "line": 144, "context": "<!-- 视频链接 -->"}, {"text": "相关视频", "file": "src/views/PostDetailView.vue", "line": 146, "context": "<dt class=\"text-sm font-medium text-gray-500 mb-2\">相关视频</dt>"}, {"text": "查看视频", "file": "src/views/PostDetailView.vue", "line": 153, "context": "查看视频 →"}, {"text": "行动按钮", "file": "src/views/PostDetailView.vue", "line": 158, "context": "<!-- 行动按钮 -->"}, {"text": "提供线索按钮", "file": "src/views/PostDetailView.vue", "line": 161, "context": "<!-- 提供线索按钮 -->"}, {"text": "我好像见过它", "file": "src/views/PostDetailView.vue", "line": 167, "context": "我好像见过它"}, {"text": "分享按钮", "file": "src/views/PostDetailView.vue", "line": 170, "context": "<!-- 分享按钮 -->"}, {"text": "分享帮助寻找", "file": "src/views/PostDetailView.vue", "line": 175, "context": "分享帮助寻找"}, {"text": "分享提示", "file": "src/views/PostDetailView.vue", "line": 179, "context": "<!-- 分享提示 -->"}, {"text": "您的帮助对找回走失宠物非常重要", "file": "src/views/PostDetailView.vue", "line": 181, "context": "您的帮助对找回走失宠物非常重要，请分享给更多人"}, {"text": "请分享给更多人", "file": "src/views/PostDetailView.vue", "line": 181, "context": "您的帮助对找回走失宠物非常重要，请分享给更多人"}, {"text": "成功提示", "file": "src/views/PostDetailView.vue", "line": 185, "context": "<!-- 成功提示 -->"}, {"text": "线索提交成功", "file": "src/views/PostDetailView.vue", "line": 195, "context": "线索提交成功！"}, {"text": "感谢您提供的线索", "file": "src/views/PostDetailView.vue", "line": 198, "context": "<p>感谢您提供的线索，失主会收到通知。</p>"}, {"text": "失主会收到通知", "file": "src/views/PostDetailView.vue", "line": 198, "context": "<p>感谢您提供的线索，失主会收到通知。</p>"}, {"text": "错误状态", "file": "src/views/PostDetailView.vue", "line": 205, "context": "<!-- 错误状态 -->"}, {"text": "帖子不存在", "file": "src/views/PostDetailView.vue", "line": 210, "context": "<h3 class=\"mt-2 text-sm font-medium text-gray-900\">帖子不存在</h3>"}, {"text": "该帖子可能已被删除或不存在", "file": "src/views/PostDetailView.vue", "line": 212, "context": "该帖子可能已被删除或不存在"}, {"text": "返回列表", "file": "src/views/PostDetailView.vue", "line": 219, "context": "返回列表"}, {"text": "线索提交表单", "file": "src/views/PostDetailView.vue", "line": 225, "context": "<!-- 线索提交表单 -->"}, {"text": "分享模态框", "file": "src/views/PostDetailView.vue", "line": 233, "context": "<!-- 分享模态框 -->"}, {"text": "状态", "file": "src/views/PostDetailView.vue", "line": 254, "context": "// 状态"}, {"text": "方法", "file": "src/views/PostDetailView.vue", "line": 261, "context": "// 方法"}, {"text": "加载帖子失败", "file": "src/views/PostDetailView.vue", "line": 273, "context": "console.error('加载帖子失败:', error)"}, {"text": "导航栏", "file": "src/views/PostsView.vue", "line": 3, "context": "<!-- 导航栏 -->"}, {"text": "走失宠物协寻平台", "file": "src/views/PostsView.vue", "line": 9, "context": "走失宠物协寻平台"}, {"text": "发布走失信息", "file": "src/views/PostsView.vue", "line": 19, "context": "发布走失信息"}, {"text": "我的账户", "file": "src/views/PostsView.vue", "line": 25, "context": "我的账户"}, {"text": "登出", "file": "src/views/PostsView.vue", "line": 31, "context": "登出"}, {"text": "登录", "file": "src/views/PostsView.vue", "line": 39, "context": "登录"}, {"text": "注册", "file": "src/views/PostsView.vue", "line": 45, "context": "注册"}, {"text": "页面标题和视图切换", "file": "src/views/PostsView.vue", "line": 54, "context": "<!-- 页面标题和视图切换 -->"}, {"text": "寻宠信息", "file": "src/views/PostsView.vue", "line": 57, "context": "<h1 class=\"text-3xl font-bold text-gray-900\">寻宠信息</h1>"}, {"text": "帮助走失的宠物回家", "file": "src/views/PostsView.vue", "line": 58, "context": "<p class=\"mt-2 text-gray-600\">帮助走失的宠物回家</p>"}, {"text": "视图切换", "file": "src/views/PostsView.vue", "line": 62, "context": "<!-- 视图切换 -->"}, {"text": "列表视图", "file": "src/views/PostsView.vue", "line": 73, "context": "列表视图"}, {"text": "地图视图", "file": "src/views/PostsView.vue", "line": 84, "context": "地图视图"}, {"text": "筛选器侧边栏", "file": "src/views/PostsView.vue", "line": 91, "context": "<!-- 筛选器侧边栏 -->"}, {"text": "筛选条件", "file": "src/views/PostsView.vue", "line": 94, "context": "<h3 class=\"text-lg font-medium text-gray-900 mb-4\">筛选条件</h3>"}, {"text": "关键词搜索", "file": "src/views/PostsView.vue", "line": 96, "context": "<!-- 关键词搜索 -->"}, {"text": "关键词搜索", "file": "src/views/PostsView.vue", "line": 99, "context": "关键词搜索"}, {"text": "搜索宠物名字", "file": "src/views/PostsView.vue", "line": 104, "context": "placeholder=\"搜索宠物名字、描述等...\""}, {"text": "描述等", "file": "src/views/PostsView.vue", "line": 104, "context": "placeholder=\"搜索宠物名字、描述等...\""}, {"text": "宠物品种", "file": "src/views/PostsView.vue", "line": 110, "context": "<!-- 宠物品种 -->"}, {"text": "宠物品种", "file": "src/views/PostsView.vue", "line": 113, "context": "宠物品种"}, {"text": "全部品种", "file": "src/views/PostsView.vue", "line": 116, "context": "<option value=\"\">全部品种</option>"}, {"text": "毛色", "file": "src/views/PostsView.vue", "line": 123, "context": "<!-- 毛色 -->"}, {"text": "毛色", "file": "src/views/PostsView.vue", "line": 126, "context": "毛色"}, {"text": "全部毛色", "file": "src/views/PostsView.vue", "line": 129, "context": "<option value=\"\">全部毛色</option>"}, {"text": "性别", "file": "src/views/PostsView.vue", "line": 136, "context": "<!-- 性别 -->"}, {"text": "性别", "file": "src/views/PostsView.vue", "line": 139, "context": "性别"}, {"text": "全部性别", "file": "src/views/PostsView.vue", "line": 142, "context": "<option value=\"\">全部性别</option>"}, {"text": "地区", "file": "src/views/PostsView.vue", "line": 149, "context": "<!-- 地区 -->"}, {"text": "走失地区", "file": "src/views/PostsView.vue", "line": 152, "context": "走失地区"}, {"text": "全部地区", "file": "src/views/PostsView.vue", "line": 155, "context": "<option value=\"\">全部地区</option>"}, {"text": "时间范围", "file": "src/views/PostsView.vue", "line": 162, "context": "<!-- 时间范围 -->"}, {"text": "走失时间", "file": "src/views/PostsView.vue", "line": 165, "context": "走失时间"}, {"text": "清除筛选", "file": "src/views/PostsView.vue", "line": 183, "context": "<!-- 清除筛选 -->"}, {"text": "清除筛选", "file": "src/views/PostsView.vue", "line": 188, "context": "清除筛选"}, {"text": "主要内容区域", "file": "src/views/PostsView.vue", "line": 193, "context": "<!-- 主要内容区域 -->"}, {"text": "列表视图", "file": "src/views/PostsView.vue", "line": 195, "context": "<!-- 列表视图 -->"}, {"text": "加载状态", "file": "src/views/PostsView.vue", "line": 197, "context": "<!-- 加载状态 -->"}, {"text": "帖子列表", "file": "src/views/PostsView.vue", "line": 202, "context": "<!-- 帖子列表 -->"}, {"text": "宠物照片", "file": "src/views/PostsView.vue", "line": 211, "context": "<!-- 宠物照片 -->"}, {"text": "帖子信息", "file": "src/views/PostsView.vue", "line": 229, "context": "<!-- 帖子信息 -->"}, {"text": "未知", "file": "src/views/PostsView.vue", "line": 233, "context": "{{ post.pet?.name || '未知' }}"}, {"text": "操作按钮", "file": "src/views/PostsView.vue", "line": 277, "context": "<!-- 操作按钮 -->"}, {"text": "查看详情", "file": "src/views/PostsView.vue", "line": 283, "context": "查看详情"}, {"text": "分享", "file": "src/views/PostsView.vue", "line": 289, "context": "分享"}, {"text": "空状态", "file": "src/views/PostsView.vue", "line": 297, "context": "<!-- 空状态 -->"}, {"text": "暂无寻宠信息", "file": "src/views/PostsView.vue", "line": 302, "context": "<h3 class=\"mt-2 text-sm font-medium text-gray-900\">暂无寻宠信息</h3>"}, {"text": "尝试调整筛选条件", "file": "src/views/PostsView.vue", "line": 304, "context": "{{ hasFilters ? '尝试调整筛选条件' : '还没有人发布寻宠信息' }}"}, {"text": "还没有人发布寻宠信息", "file": "src/views/PostsView.vue", "line": 304, "context": "{{ hasFilters ? '尝试调整筛选条件' : '还没有人发布寻宠信息' }}"}, {"text": "分页", "file": "src/views/PostsView.vue", "line": 308, "context": "<!-- 分页 -->"}, {"text": "地图视图", "file": "src/views/PostsView.vue", "line": 319, "context": "<!-- 地图视图 -->"}, {"text": "分享模态框", "file": "src/views/PostsView.vue", "line": 331, "context": "<!-- 分享模态框 -->"}, {"text": "状态", "file": "src/views/PostsView.vue", "line": 355, "context": "// 状态"}, {"text": "分享相关状态", "file": "src/views/PostsView.vue", "line": 366, "context": "// 分享相关状态"}, {"text": "筛选条件", "file": "src/views/PostsView.vue", "line": 370, "context": "// 筛选条件"}, {"text": "计算属性", "file": "src/views/PostsView.vue", "line": 381, "context": "// 计算属性"}, {"text": "方法", "file": "src/views/PostsView.vue", "line": 386, "context": "// 方法"}, {"text": "服务器返回的格式是", "file": "src/views/PostsView.vue", "line": 399, "context": "// 服务器返回的格式是 { success, message, data: [...], pagination: {...} }"}, {"text": "而不是", "file": "src/views/PostsView.vue", "line": 400, "context": "// 而不是 { success, message, data: { items: [...], pagination: {...} } }"}, {"text": "从响应中获取分页信息", "file": "src/views/PostsView.vue", "line": 402, "context": "// 从响应中获取分页信息"}, {"text": "加载帖子失败", "file": "src/views/PostsView.vue", "line": 413, "context": "console.error('加载帖子失败:', error)"}, {"text": "滚动到顶部", "file": "src/views/PostsView.vue", "line": 444, "context": "// 滚动到顶部"}, {"text": "监听视图模式变化", "file": "src/views/PostsView.vue", "line": 467, "context": "// 监听视图模式变化"}, {"text": "创建新账户", "file": "src/views/RegisterView.vue", "line": 6, "context": "创建新账户"}, {"text": "已有账户", "file": "src/views/RegisterView.vue", "line": 9, "context": "已有账户？"}, {"text": "立即登录", "file": "src/views/RegisterView.vue", "line": 14, "context": "立即登录"}, {"text": "用户名", "file": "src/views/RegisterView.vue", "line": 23, "context": "用户名"}, {"text": "请输入用户名", "file": "src/views/RegisterView.vue", "line": 32, "context": "placeholder=\"请输入用户名\""}, {"text": "邮箱地址", "file": "src/views/RegisterView.vue", "line": 38, "context": "邮箱地址"}, {"text": "请输入邮箱地址", "file": "src/views/RegisterView.vue", "line": 48, "context": "placeholder=\"请输入邮箱地址\""}, {"text": "手机号码", "file": "src/views/RegisterView.vue", "line": 54, "context": "手机号码（可选）"}, {"text": "可选", "file": "src/views/RegisterView.vue", "line": 54, "context": "手机号码（可选）"}, {"text": "请输入手机号码", "file": "src/views/RegisterView.vue", "line": 62, "context": "placeholder=\"请输入手机号码\""}, {"text": "密码", "file": "src/views/RegisterView.vue", "line": 68, "context": "密码"}, {"text": "请输入密码", "file": "src/views/RegisterView.vue", "line": 78, "context": "placeholder=\"请输入密码（至少6位）\""}, {"text": "至少", "file": "src/views/RegisterView.vue", "line": 78, "context": "placeholder=\"请输入密码（至少6位）\""}, {"text": "确认密码", "file": "src/views/RegisterView.vue", "line": 84, "context": "确认密码"}, {"text": "请再次输入密码", "file": "src/views/RegisterView.vue", "line": 94, "context": "placeholder=\"请再次输入密码\""}, {"text": "我同意", "file": "src/views/RegisterView.vue", "line": 109, "context": "我同意"}, {"text": "服务条款", "file": "src/views/RegisterView.vue", "line": 110, "context": "<a href=\"#\" class=\"text-primary-600 hover:text-primary-500\">服务条款</a>"}, {"text": "隐私政策", "file": "src/views/RegisterView.vue", "line": 112, "context": "<a href=\"#\" class=\"text-primary-600 hover:text-primary-500\">隐私政策</a>"}, {"text": "错误提示", "file": "src/views/RegisterView.vue", "line": 116, "context": "<!-- 错误提示 -->"}, {"text": "注册中", "file": "src/views/RegisterView.vue", "line": 139, "context": "{{ authStore.isLoading ? '注册中...' : '注册' }}"}, {"text": "注册", "file": "src/views/RegisterView.vue", "line": 139, "context": "{{ authStore.isLoading ? '注册中...' : '注册' }}"}, {"text": "表单数据", "file": "src/views/RegisterView.vue", "line": 157, "context": "// 表单数据"}, {"text": "验证错误", "file": "src/views/RegisterView.vue", "line": 168, "context": "// 验证错误"}, {"text": "请输入有效的邮箱地址", "file": "src/views/RegisterView.vue", "line": 179, "context": "return '请输入有效的邮箱地址'"}, {"text": "请输入有效的香港手机号码", "file": "src/views/RegisterView.vue", "line": 183, "context": "return '请输入有效的香港手机号码'"}, {"text": "密码至少需要", "file": "src/views/RegisterView.vue", "line": 191, "context": "return '密码至少需要6个字符'"}, {"text": "个字符", "file": "src/views/RegisterView.vue", "line": 191, "context": "return '密码至少需要6个字符'"}, {"text": "两次输入的密码不一致", "file": "src/views/RegisterView.vue", "line": 195, "context": "return '两次输入的密码不一致'"}, {"text": "请同意服务条款和隐私政策", "file": "src/views/RegisterView.vue", "line": 199, "context": "return '请同意服务条款和隐私政策'"}, {"text": "处理注册", "file": "src/views/RegisterView.vue", "line": 205, "context": "// 处理注册"}, {"text": "清除之前的错误", "file": "src/views/RegisterView.vue", "line": 207, "context": "// 清除之前的错误"}, {"text": "验证表单", "file": "src/views/RegisterView.vue", "line": 210, "context": "// 验证表单"}, {"text": "基础验证", "file": "src/views/RegisterView.vue", "line": 215, "context": "// 基础验证"}, {"text": "注册成功", "file": "src/views/RegisterView.vue", "line": 230, "context": "// 注册成功，跳转到首页"}, {"text": "跳转到首页", "file": "src/views/RegisterView.vue", "line": 230, "context": "// 注册成功，跳转到首页"}, {"text": "清除之前的错误", "file": "src/views/RegisterView.vue", "line": 236, "context": "// 清除之前的错误"}]