<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <router-link to="/" class="text-xl font-bold text-gray-900">
              {{ $t('app.title') }}
            </router-link>
          </div>

          <div class="flex items-center space-x-4">
            <!-- 语言切换 -->
            <LanguageSwitch />

            <router-link
              to="/posts"
              class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
            >
              {{ $t('nav.posts') }}
            </router-link>
            <router-link
              to="/post/create"
              class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              {{ $t('post.create') }}
            </router-link>
            <button
              @click="handleLogout"
              class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
            >
              {{ $t('auth.logout') }}
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">{{ $t('nav.dashboard') }}</h1>
        <p class="mt-2 text-gray-600">{{ $t('dashboard.description') }}</p>
      </div>

      <!-- 侧边栏导航 -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <div class="lg:col-span-1">
          <nav class="bg-white rounded-lg shadow p-6">
            <ul class="space-y-2">
              <li>
                <button
                  @click="activeTab = 'overview'"
                  :class="[
                    'w-full text-left px-3 py-2 rounded-md text-sm font-medium',
                    activeTab === 'overview'
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  ]"
                >
                  {{ $t('dashboard.overview') }}
                </button>
              </li>
              <li>
                <button
                  @click="activeTab = 'posts'"
                  :class="[
                    'w-full text-left px-3 py-2 rounded-md text-sm font-medium',
                    activeTab === 'posts'
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  ]"
                >
                  {{ $t('dashboard.myPosts') }}
                </button>
              </li>
              <li>
                <button
                  @click="activeTab = 'pets'"
                  :class="[
                    'w-full text-left px-3 py-2 rounded-md text-sm font-medium',
                    activeTab === 'pets'
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  ]"
                >
                  {{ $t('dashboard.myPets') }}
                </button>
              </li>
              <li>
                <button
                  @click="activeTab = 'profile'"
                  :class="[
                    'w-full text-left px-3 py-2 rounded-md text-sm font-medium',
                    activeTab === 'profile'
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  ]"
                >
                  {{ $t('dashboard.profile') }}
                </button>
              </li>
            </ul>
          </nav>
        </div>

        <!-- 主要内容区域 -->
        <div class="lg:col-span-3">
          <!-- 概览 -->
          <div v-if="activeTab === 'overview'">
            <DashboardOverview />
          </div>

          <!-- 我的帖子 -->
          <div v-else-if="activeTab === 'posts'">
            <MyPosts />
          </div>

          <!-- 我的宠物 -->
          <div v-else-if="activeTab === 'pets'">
            <MyPets />
          </div>

          <!-- 个人资料 -->
          <div v-else-if="activeTab === 'profile'">
            <UserProfile />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import DashboardOverview from '@/components/dashboard/DashboardOverview.vue'
import MyPosts from '@/components/dashboard/MyPosts.vue'
import MyPets from '@/components/dashboard/MyPets.vue'
import UserProfile from '@/components/dashboard/UserProfile.vue'
import LanguageSwitch from '@/components/LanguageSwitch.vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 状态
const activeTab = ref('overview')

// 方法
const handleLogout = async () => {
  await authStore.logout()
  router.push('/')
}

onMounted(() => {
  // 检查用户是否已登录
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // 根据查询参数设置活动标签页
  const tab = route.query.tab as string
  if (tab && ['overview', 'posts', 'pets', 'profile'].includes(tab)) {
    activeTab.value = tab
  }
})
</script>
