<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">国际化功能测试</h1>
        <p class="mt-2 text-gray-600">测试简体中文和香港繁体中文的切换效果</p>
      </div>

      <!-- 语言切换 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">语言切换</h2>
        <div class="flex items-center space-x-4">
          <span>当前语言：</span>
          <LanguageSwitch />
          <span class="text-sm text-gray-500">
            ({{ getCurrentLocale() }} - {{ getLocaleName(getCurrentLocale()) }})
          </span>
        </div>
      </div>

      <!-- 应用信息测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">应用信息</h2>
        <div class="space-y-2">
          <p><strong>标题：</strong>{{ $t('app.title') }}</p>
          <p><strong>副标题：</strong>{{ $t('app.subtitle') }}</p>
          <p><strong>描述：</strong>{{ $t('app.description') }}</p>
        </div>
      </div>

      <!-- 导航测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">导航菜单</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center p-4 border rounded">
            <p class="font-medium">{{ $t('nav.home') }}</p>
          </div>
          <div class="text-center p-4 border rounded">
            <p class="font-medium">{{ $t('nav.posts') }}</p>
          </div>
          <div class="text-center p-4 border rounded">
            <p class="font-medium">{{ $t('nav.login') }}</p>
          </div>
          <div class="text-center p-4 border rounded">
            <p class="font-medium">{{ $t('nav.register') }}</p>
          </div>
        </div>
      </div>

      <!-- 认证相关测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">认证相关</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 class="font-medium mb-2">登录</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('auth.loginTitle') }}</li>
              <li>{{ $t('auth.email') }}</li>
              <li>{{ $t('auth.password') }}</li>
              <li>{{ $t('auth.rememberMe') }}</li>
              <li>{{ $t('auth.forgotPassword') }}</li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-2">注册</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('auth.registerTitle') }}</li>
              <li>{{ $t('auth.username') }}</li>
              <li>{{ $t('auth.confirmPassword') }}</li>
              <li>{{ $t('auth.registerNow') }}</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 帖子相关测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">帖子管理</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 class="font-medium mb-2">状态</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('post.status.searching') }}</li>
              <li>{{ $t('post.status.found') }}</li>
              <li>{{ $t('post.status.closed') }}</li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-2">操作</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('post.actions.publish') }}</li>
              <li>{{ $t('post.actions.save') }}</li>
              <li>{{ $t('post.actions.edit') }}</li>
              <li>{{ $t('post.actions.delete') }}</li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-2">字段</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('post.fields.petName') }}</li>
              <li>{{ $t('post.fields.petSpecies') }}</li>
              <li>{{ $t('post.fields.description') }}</li>
              <li>{{ $t('post.fields.location') }}</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 宠物信息测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">宠物信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 class="font-medium mb-2">品种</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('pet.species.dog') }}</li>
              <li>{{ $t('pet.species.cat') }}</li>
              <li>{{ $t('pet.species.rabbit') }}</li>
              <li>{{ $t('pet.species.other') }}</li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-2">性别</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('pet.gender.male') }}</li>
              <li>{{ $t('pet.gender.female') }}</li>
              <li>{{ $t('pet.gender.unknown') }}</li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-2">毛色</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('pet.colors.black') }}</li>
              <li>{{ $t('pet.colors.white') }}</li>
              <li>{{ $t('pet.colors.brown') }}</li>
              <li>{{ $t('pet.colors.mixed') }}</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 通用操作测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">通用操作</h2>
        <div class="flex flex-wrap gap-2">
          <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">{{ $t('common.save') }}</span>
          <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">{{ $t('common.cancel') }}</span>
          <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">{{ $t('common.delete') }}</span>
          <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">{{ $t('common.edit') }}</span>
          <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">{{ $t('common.view') }}</span>
          <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">{{ $t('common.search') }}</span>
        </div>
      </div>

      <!-- 消息提示测试 -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">消息提示</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 class="font-medium mb-2 text-green-600">成功消息</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('message.success.login') }}</li>
              <li>{{ $t('message.success.save') }}</li>
              <li>{{ $t('message.success.upload') }}</li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-2 text-red-600">错误消息</h3>
            <ul class="space-y-1 text-sm">
              <li>{{ $t('message.error.network') }}</li>
              <li>{{ $t('message.error.server') }}</li>
              <li>{{ $t('message.error.unauthorized') }}</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 返回按钮 -->
      <div class="text-center">
        <router-link
          to="/"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
        >
          {{ $t('common.back') }}
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import LanguageSwitch from '@/components/LanguageSwitch.vue'
import { getCurrentLocale, getLocaleName } from '@/locales'
</script>
